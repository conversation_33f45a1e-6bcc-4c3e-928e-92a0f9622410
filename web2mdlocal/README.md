# web2mdlocal

Python version of web2md for batch processing HTML files to Markdown.

## Features

- 📁 **Batch Processing**: Convert all HTML/HTM files in a folder and subdirectories
- 🧹 **Smart Cleaning**: Removes scripts, styles, navigation, and ads
- 🎯 **Content Extraction**: Attempts to find main content using common selectors
- 📝 **Clean Markdown**: Post-processes output for better formatting
- 🔧 **Error Handling**: Skips problematic files with detailed error reporting
- 📊 **Progress Tracking**: Shows processing status for each file
- 🗂️ **Recursive Search**: Processes files in subdirectories maintaining folder structure
- 🔤 **Space Handling**: Properly handles file and folder names with spaces and special characters

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

## Usage

```bash
python web2md.py <input_folder> <output_folder>
```

### Examples

```bash
# Convert all HTML files in 'html_files' folder to 'markdown_files' folder
python web2md.py html_files markdown_files

# Using absolute paths
python web2md.py /path/to/html/files /path/to/output/markdown

# Paths with spaces (use quotes)
python web2md.py "My HTML Files" "My Markdown Files"
python web2md.py "/Users/<USER>/Documents/Web Pages" "/Users/<USER>/Documents/Markdown Output"

# Mixed paths
python web2md.py "html files with spaces" simple_output
```

### Arguments

- `input_folder`: Path to folder containing HTML/HTM files
- `output_folder`: Path to output folder for Markdown files (will be created if it doesn't exist)

## Features

### HTML Cleaning
- Removes `<script>`, `<style>`, and `<noscript>` tags
- Strips navigation, header, footer, and sidebar elements
- Removes ads and promotional content
- Cleans up comments and unwanted elements

### Content Extraction
Attempts to find main content using these selectors (in order):
- `<article>`
- `<main>`
- `[role="main"]`
- `.content`, `.post-content`, `.entry-content`
- `.article-content`, `#content`, `#main-content`

### Output Processing
- **Link Removal**: Strips all URLs and links, keeping only text content
- **Code Preservation**: Maintains code blocks and formatting
- **Clean Formatting**: Removes excessive blank lines and artifacts
- **List Cleanup**: Cleans up list formatting and removes empty items
- **Content Focus**: Removes "Copy" buttons and navigation elements
- **File Structure**: Preserves original filename with `.md` extension
- **Folder Structure**: Maintains folder structure in output directory

### Path Handling
- Supports paths with spaces (use quotes: `"My Folder"`)
- Handles special characters in file and folder names
- Works with both relative and absolute paths
- Recursive processing of subdirectories

## Requirements

- Python 3.6+
- beautifulsoup4
- html2text
- lxml (recommended HTML parser)

## Example Output

Input HTML:
```html
<article>
    <h1>Sample Article</h1>
    <p>This is <strong>bold</strong> and <em>italic</em> text.</p>
    <ul>
        <li>First item</li>
        <li>Second item</li>
    </ul>
</article>
```

Output Markdown:
```markdown
# Sample Article

This is **bold** and _italic_ text.

  * First item
  * Second item
```
