#!/usr/bin/env python3
"""
web2md - Convert HTML files to Markdown in batch

A Python tool for converting HTML files in a folder to Markdown files,
similar to the JavaScript web2md but for local batch processing.
"""

import argparse
import os
import sys
from pathlib import Path
from html_to_markdown import convert_html_to_markdown, get_title_from_html


def find_html_files(input_folder):
    """
    Find all HTML and HTM files in the input folder and subdirectories.

    Args:
        input_folder (Path): Input folder path

    Returns:
        list: List of HTML file paths
    """
    html_files = []

    # Find .html and .htm files recursively
    for extension in ['*.html', '*.htm']:
        html_files.extend(input_folder.rglob(extension))
        # Also search case-insensitive
        html_files.extend(input_folder.rglob(extension.upper()))

    return sorted(html_files)


def create_output_filename(html_file, input_folder, output_folder):
    """
    Create output filename for markdown file.

    Args:
        html_file (Path): Input HTML file path
        input_folder (Path): Input folder path
        output_folder (Path): Output folder path

    Returns:
        Path: Output markdown file path
    """
    # Get relative path from input folder
    relative_path = html_file.relative_to(input_folder)

    # Change extension to .md
    md_filename = relative_path.with_suffix('.md')

    # Create full output path
    output_path = output_folder / md_filename

    # Ensure output directory exists
    output_path.parent.mkdir(parents=True, exist_ok=True)

    return output_path


def process_html_file(html_file, output_file):
    """
    Process a single HTML file and convert to Markdown.

    Args:
        html_file (Path): Input HTML file path
        output_file (Path): Output markdown file path

    Returns:
        tuple: (success: bool, error_message: str)
    """
    try:
        # Ensure paths are properly resolved (handles spaces and special characters)
        html_file = Path(html_file).resolve()
        output_file = Path(output_file).resolve()

        # Read HTML file
        with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
            html_content = f.read()

        # Convert to markdown
        markdown_content = convert_html_to_markdown(html_content)

        # Get title for metadata
        title = get_title_from_html(html_content)

        # Add metadata header if title exists
        if title:
            metadata_header = f"# {title}\n\n"
            if not markdown_content.startswith('# '):
                markdown_content = metadata_header + markdown_content

        # Write markdown file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        return True, ""

    except Exception as e:
        return False, str(e)


def main():
    """Main function to handle command line arguments and process files."""
    parser = argparse.ArgumentParser(
        description='Convert HTML files to Markdown in batch',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python web2md.py html_files markdown_files
  python web2md.py /path/to/html /path/to/output
  python web2md.py "My HTML Files" "My Markdown Files"

Note: Use quotes around paths containing spaces.
        """
    )

    parser.add_argument(
        'input_folder',
        help='Path to folder containing HTML/HTM files (use quotes for paths with spaces)'
    )

    parser.add_argument(
        'output_folder',
        help='Path to output folder for Markdown files (will be created if needed)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Show detailed processing information'
    )

    args = parser.parse_args()

    # Convert to Path objects and handle spaces in paths
    input_folder = Path(args.input_folder.strip('"\'').strip()).resolve()
    output_folder = Path(args.output_folder.strip('"\'').strip()).resolve()

    # Validate input folder
    if not input_folder.exists():
        print(f"Error: Input folder '{input_folder}' does not exist.")
        sys.exit(1)

    if not input_folder.is_dir():
        print(f"Error: '{input_folder}' is not a directory.")
        sys.exit(1)

    # Create output folder if it doesn't exist
    try:
        output_folder.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        print(f"Error: Could not create output folder '{output_folder}': {e}")
        sys.exit(1)

    # Find HTML files
    html_files = find_html_files(input_folder)

    if not html_files:
        print(f"No HTML files found in '{input_folder}'")
        sys.exit(0)

    print(f"Found {len(html_files)} HTML file(s) in '{input_folder}'")
    print(f"Output folder: '{output_folder}'")
    print()

    # Process files
    successful = 0
    failed = 0

    for i, html_file in enumerate(html_files, 1):
        output_file = create_output_filename(html_file, input_folder, output_folder)

        if args.verbose:
            print(f"[{i}/{len(html_files)}] Processing: {html_file.name}")
        else:
            print(f"[{i}/{len(html_files)}] {html_file.name} -> {output_file.name}")

        success, error = process_html_file(html_file, output_file)

        if success:
            successful += 1
            if args.verbose:
                print(f"  ✓ Successfully converted to: {output_file}")
        else:
            failed += 1
            print(f"  ✗ Error: {error}")

    # Summary
    print()
    print(f"Conversion complete!")
    print(f"  Successful: {successful}")
    print(f"  Failed: {failed}")
    print(f"  Total: {len(html_files)}")

    if failed > 0:
        sys.exit(1)


if __name__ == '__main__':
    main()
