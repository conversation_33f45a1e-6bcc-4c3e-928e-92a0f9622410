"""
HTML to Markdown conversion utilities.

This module provides functions to convert HTML content to clean Markdown,
similar to the JavaScript version using Turndown.js.
"""

import re
from bs4 import BeautifulSoup, Comment
import html2text


def extract_main_content(html_content):
    """
    Extract main content from HTML using common content selectors.

    Args:
        html_content (str): Raw HTML content

    Returns:
        str: Extracted main content HTML
    """
    soup = BeautifulSoup(html_content, 'lxml')

    # Try to find main content using common selectors
    content_selectors = [
        'article',
        'main',
        '[role="main"]',
        '.content',
        '.post-content',
        '.entry-content',
        '.article-content',
        '#content',
        '#main-content',
        '.main-content'
    ]

    for selector in content_selectors:
        element = soup.select_one(selector)
        if element and element.get_text(strip=True) and len(element.get_text(strip=True)) > 100:
            return str(element)

    # If no main content found, try to extract from body
    body = soup.find('body')
    if body:
        # Remove unwanted elements from body
        unwanted_selectors = [
            'nav', 'header', 'footer', 'aside',
            '.navigation', '.sidebar', '.menu',
            '.ads', '.advertisement', '.banner',
            'script', 'style', 'noscript'
        ]

        for selector in unwanted_selectors:
            for element in body.select(selector):
                element.decompose()

        return str(body)

    # Fallback to original HTML
    return html_content


def clean_html(html_content):
    """
    Clean HTML content before conversion to Markdown.

    Args:
        html_content (str): Raw HTML content

    Returns:
        str: Cleaned HTML content
    """
    soup = BeautifulSoup(html_content, 'lxml')

    # Remove comments
    for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
        comment.extract()

    # Remove script, style, and noscript tags
    for tag in soup.find_all(['script', 'style', 'noscript']):
        tag.decompose()

    # Remove navigation, footer, sidebar elements
    unwanted_tags = ['nav', 'footer', 'aside', 'header']
    for tag_name in unwanted_tags:
        for tag in soup.find_all(tag_name):
            tag.decompose()

    # Remove elements with ad-related classes
    ad_classes = ['ad', 'ads', 'advertisement', 'banner', 'promo', 'promotion']
    for class_name in ad_classes:
        for element in soup.find_all(class_=lambda x: x and any(ad in str(x).lower() for ad in ad_classes)):
            element.decompose()

    return str(soup)


def post_process_markdown(markdown_content):
    """
    Post-process markdown content to improve formatting and remove unwanted elements.

    Args:
        markdown_content (str): Raw markdown content

    Returns:
        str: Cleaned markdown content
    """
    # Remove excessive blank lines (more than 2 consecutive)
    processed = re.sub(r'\n{3,}', '\n\n', markdown_content)

    # Clean up list formatting - remove empty list items
    processed = re.sub(r'^\s*[-*+]\s*$', '', processed, flags=re.MULTILINE)

    # Remove any remaining link artifacts
    processed = re.sub(r'\[\]\([^)]*\)', '', processed)
    processed = re.sub(r'\[([^\]]+)\]\([^)]*\)', r'\1', processed)  # Convert [text](url) to just text

    # Remove empty emphasis
    processed = re.sub(r'\*\*\*\*', '', processed)
    processed = re.sub(r'____', '', processed)

    # Clean up common artifacts from web pages
    processed = re.sub(r'^\s*Copy\s*$', '', processed, flags=re.MULTILINE)  # Remove "Copy" buttons
    processed = re.sub(r'^\s*__\s*$', '', processed, flags=re.MULTILINE)   # Remove standalone underscores

    # Improve code block formatting
    processed = re.sub(r'```\s*\n\s*```', '', processed)  # Remove empty code blocks

    # Clean up excessive whitespace but preserve code formatting
    lines = processed.split('\n')
    cleaned_lines = []
    in_code_block = False

    for line in lines:
        if line.strip().startswith('```'):
            in_code_block = not in_code_block
            cleaned_lines.append(line)
        elif in_code_block:
            cleaned_lines.append(line)  # Preserve code formatting
        else:
            cleaned_lines.append(line.rstrip())  # Remove trailing whitespace from non-code lines

    processed = '\n'.join(cleaned_lines)

    # Final cleanup
    processed = processed.strip()

    # Ensure single newline at end
    if processed and not processed.endswith('\n'):
        processed += '\n'

    return processed


def convert_html_to_markdown(html_content, extract_content=True):
    """
    Convert HTML content to Markdown.

    Args:
        html_content (str): HTML content to convert
        extract_content (bool): Whether to extract main content first

    Returns:
        str: Converted Markdown content
    """
    try:
        # Extract main content if requested
        if extract_content:
            html_content = extract_main_content(html_content)

        # Clean the HTML
        cleaned_html = clean_html(html_content)

        # Convert to markdown using html2text
        h = html2text.HTML2Text()
        h.ignore_links = True  # Remove all links, keep only text content
        h.ignore_images = True  # Remove images
        h.ignore_emphasis = False  # Keep bold/italic formatting
        h.body_width = 0  # Don't wrap lines
        h.unicode_snob = True
        h.skip_internal_links = True

        markdown = h.handle(cleaned_html)

        # Post-process the markdown
        return post_process_markdown(markdown)

    except Exception as e:
        return f"Error converting HTML to Markdown: {str(e)}\n"


def get_title_from_html(html_content):
    """
    Extract title from HTML content.

    Args:
        html_content (str): HTML content

    Returns:
        str: Extracted title or empty string
    """
    try:
        soup = BeautifulSoup(html_content, 'lxml')
        title_tag = soup.find('title')
        if title_tag:
            return title_tag.get_text(strip=True)

        # Try h1 as fallback
        h1_tag = soup.find('h1')
        if h1_tag:
            return h1_tag.get_text(strip=True)

        return ""
    except Exception:
        return ""
