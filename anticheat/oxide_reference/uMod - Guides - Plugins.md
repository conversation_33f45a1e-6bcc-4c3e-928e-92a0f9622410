# uMod - Guides - Plugins

5 minutes to read

Created by

<PERSON><PERSON>

Updated by

Wulf

#  Plugins

####  Details about basic plugin usage

This guide is for uMod,_**not** Oxide_.

  * Getting Started
  * Versioning
  * Hooks
  * Commands
  * Configuration
  * Localization

All plugins are written using the C# programming language.

uMod provides core components and a consistent game-agnostic API for implementing plugins universally across all supported games.

uMod is not affilliated with game developers and plugin developers will often need to familiarize themselves with the API provided by the game. Game-specific API's can be obtained with a decompiler, such as dotPeek.

uMod provides common functionality to easily scaffold commands, configuration, localization, and more. Additionally uMod provides hundreds of hooks to modify or listen for game-specific events. However, uMod does not document or support the game-specific API's.

## Getting Started

Below is a minimal example of a uMod plugin.

> `umod/plugins/EpicStuff.cs`

     namespace uMod.Plugins
    {
        [Info("Epic Stuff", "Unknown Author", "0.1.0")]
        [Description("Makes epic stuff happen")]
        class EpicStuff : Plugin
        {
            private void Init()
            {
                Logger.Info("A baby plugin is born!");
            }

            // The rest of the code magic

            // TODO (you): Make more epic stuff
        }
    }

## Versioning

Any time a plugin is updated, it is expected that the version will be incremented. It is recommended to use semantic versioning to determine how a version should be incremented...

5.7.0

  * **Major** \- Breaking changes. Not backwards compatible with previous major release.
  * **Minor** \- Added functionality or features added. No breaking changes.
  * **Patch** \- Bug fixes. Backwards compatible with no change in functionality.

## Hooks

A hook is a method which is invoked when a player or server performs a particular action. For example, when a player chats with other players, the `OnPlayerChat` hook is invoked. The `OnPlayerChat` hook intercepts normal chat behavior and provides the opportunity for a plugin to modify or cancel the message.

More details about implementing hooks may be found in the Hooks guide.

## Commands

A command is a method that a player or server administrators may use to invoke a particular action upon _command_. Commands are typically separated into two categories: Chat commands, and Console commands.

Chat commands are in-game commands entered via the in-game chat, usually prefixed by a forward slash `/`.

Console commands may be executed from the server console or an in-game console interface (when the game provides one).

More details about implementing commands may be found in the Commands guide.

## Configuration

Plugins are quite often accompanied by a configuration file that contains options which server administrators can easily change without any knowledge of programming or fear that their changes will be overwritten with the next update.

Configuration files may be in the `JSON` or `TOML` formats. `JSON` or `JavaScript Object Notation` files have a `.json` file extension and represent lightweight human-readable data-interchange format. Similarly, `TOML` or `Tom's Obvious, Minimal Language` files have a `.toml` file extension and offer a more user-friendly alternative to `JSON`.

More details about implementing configuration files in plugins can be found in the Configuration guide.

## Localization

When a plugin needs to send a message to a player it is necessary to ensure these messages can be automatically translated into the preferred language of the recipient.

Plugins should always use a Localization provider when sending messages. English is _required_ , but plugins may provide other languages by default. Server administrators can easily add additional translations themselves using a simple file naming convention and `JSON`.

More details on how to implement localization may be found in the Localization guide.

Hooks

### The Basics

  1. **Plugins**
  2. Hooks
  3. Commands
  4. Validation
  5. Configuration
  6. Localization
  7. Schematics
  8. Filesystem
  9. Preprocessors
  10. Gates
  11. Players
  12. Integration
  13. Timers
  14. Logging
  15. Text Styling
