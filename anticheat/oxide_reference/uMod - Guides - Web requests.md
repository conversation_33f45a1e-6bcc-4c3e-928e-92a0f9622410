# uMod - Guides - Web requests

7 minutes to read

Created by

Calytic

Updated by

<PERSON><PERSON>

#  Web requests

####  Performing web requests and handling the results

This guide is for uMod,_**not** Oxide_.

  * Introduction
  * Installation
  * Usage
  * Request options
  * Request failure
  * Request cancellation
  * Request methods
  * Multi-part forms
    * Form sections
    * File uploads

## Introduction

Machines connected to the internet are separated into two categories: **clients** and **servers**.

A web client (e.g. Firefox, Chrome, etc) will typically make a "web request" using the Hypertext Transfer Protocol or HTTP/S. The web request must have a destination, such as "umod.org". When the web server at umod.org receives a web request, it creates a "web response" and sends it back.

uMod provides an integrated web client that allows for secure communication over the web using the most modern security standards. With the web client, developers can easily make all types of HTTP/S requests including `GET`, `POST`, `PUT`, etc.

## Installation

Provided the uMod Agent is available on the server, no additional installation is required. uMod will automatically detect the uMod Agent and use it to install standalone applications such as the web client.

## Usage

Every plugin has access to the web client interface by default.

    Web.Get("https://httpbin.org/get")
       .Done(delegate(WebResponse response)
       {
          if (response.StatusCode == 200)
          {
              Logger.Info(response.ReadAsString());
          }
       });

The `Done` callback function receives one argument of the type `uMod.Common.Web.WebResponse`. The `response` object contains information about the HTTP response including `StatusCode`, `StatusDescription`, `Method`, `Headers`, `ContentType`, `ContentLength`, `ContentEncoding`, `ResponseUri` and `Body`. The `Body` field is a byte array, but a string representation of the body may be obtained easily using the `ReadAsString` method as shown above.

## Request options

Specify additional parameters to customize the web request.

> Signature

     Request Web.Request(
        string Method, // HTTP request method
        string Url, // Remote uniform resource location
        IDictionary<string, string> Headers, // Dictionary of HTTP headers
        string Body, // Arbitrary request body
        IDictionary<string, string> Cookies, // Dictionary of cookies
        float Timeout // Maximum request duration
    )

> Implementation

    Dictionary<string, string> headers = new Dictionary<string, string>
    {
        { "X-Header", "My Header" }
    };

    Dictionary<string, string> cookies = new Dictionary<string, string>
    {
        { "My-Cookie", "Cookie Value" }
    };

    Web.Request(
      "POST",
      "https://httpbin.org/post",
      headers,
      "key=value&key2=value",
      cookies,
      60f
    );

When a `Body` is specified in a request, the `Content-Type: application/x-www-form-urlencoded` and `Content-Length` headers are automatically added.

## Request failure

Observe web request failure, due to a timeout or another error, with the `Fail` event callback.

    Web.Get("https://httpbin.org/get")
       .Fail(delegate(WebResponse response)
       {
          Logger.Error(response.StatusDescription);
       });

## Request cancellation

Web requests are canceled if a plugin is unloaded while the request is processing. Observe the cancellation of a web request with the `Canceled` event callback.

    Web.Get("https://httpbin.org/get")
       .Canceled(delegate()
       {
          Logger.Warning("Request canceled");
       });

## Request methods

Get data from the specified URL.

    Web.Get("https://httpbin.org/get");

Create new resources at the specified URL.

    Web.Post("https://httpbin.org/post");

Update existing resource at the specified URL.

    Web.Put("https://httpbin.org/put");

Partial update of existing resource at the specified URL.

    Web.Patch("https://httpbin.org/patch");

Delete existing resource at the specified URL.

    Web.Delete("https://httpbin.org/delete");

## Multi-part forms

The default content type is `application/x-www-form-urlencoded` for `POST` and `PUT` requests, however `application/x-www-form-urlencoded` is inefficient for sending large quantities of binary data or text. The content type `multipart/form-data` should be used for submitting forms that contain files, non-ASCII data, or binary data. To use `multipart/form-data`, simply implement the alternative `Web.Post` or `Web.Put` methods that support form sections and file uploads.

### Form sections

    Dictionary<string, string> headers = new Dictionary<string, string>
    {
        { "X-Header", "My Header" }
    };

    Dictionary<string, FormSection> form = new Dictionary<string, FormSection>
    {
        { "field", "value" }
    };

    Web.Post("https://httpbin.org/post", headers, form);

### File uploads

Create a file attachment directly.

    Dictionary<string, IMultipartFile> files = new Dictionary<string, IMultipartFile>
    {
        { "myFile", new FileAttachment("myFile.json", "{ \"json\" : true }", "application/json") }
    }

    Web.Post("https://httpbin.org/post", headers, form, files);

Use an existing data file with the `ToAttachment` helper method.

     public class MyData
    {
        public bool Test => true;
    }

    IDataFile<MyData> datafile = Files.GetDataFile<MyData>("myData", new MyData());
    Dictionary<string, IMultipartFile> files = new Dictionary<string, IMultipartFile>
    {
        { "myFile", datafile.ToAttachment() }
    };

    Web.Post("https://httpbin.org/post", headers, form, files);

`ToAttachment` will create a `FileAttachment` with the filename, attachment content, and content type specified automatically.

  1. `JsonFile` \- `application/json`
  2. `TomlFile` \- `text/plain`
  3. `BinaryFile` \- `application/octet-stream`
  4. `ProtoFile` \- `application/octet-stream`
  5. `TextFile` \- `text/plain`

Hook eventsPooling

### Advanced

  1. Hook events
  2. **Web requests**
  3. Pooling
  4. Duck typing
  5. Hook decorators
  6. Promises
  7. Database
  8. Modeling
