# uMod - Guides - Logging

4 minutes to read

Created by

Calytic

Updated by

<PERSON><PERSON>

#  Logging

####  Details about basic gate usage

This guide is for uMod,_**not** Oxide_.

  * Introduction
  * Configuration
  * Log levels
  * Write to channel
  * Customize logging
    * Configured log
    * Default log
    * Single file logger
    * Daily logger

## Introduction

uMod includes logging abstractions to log messages in a variety of ways (ex. files, game consoles, and third-party services).

## Configuration

Logging messages created via the API below will be broadcast according to the logging configuration.

## Log levels

The `level` option above will configure the minimum level of log messages that the log channel will log. The log levels are compliant with the RFC 5424 specification of the syslog protocol. The levels include: `emergency`, `alert`, `critical`, `error`, `warning`, `notice`, `info`, and `debug` in order of importance.

  * `emergency` \- System is unusable
  * `alert` \- Action must be taken immediately
  * `critical` \- Critical conditions
  * `error` \- Error conditions
  * `warning` \- Warning conditions
  * `notice` \- Normal but significant condition
  * `info` \- Informational messages
  * `debug` \- Debugging messages

If a logger is configured with the `info` level, the debug message (with a lower priority) will be ignored.

    Logger.Debug("A debug message");

## Write to channel

The methods below will broadcast a message at various log levels.

    Logger.Emergency("Emergency!");
    Logger.Alert("Alert!");
    Logger.Critical("Critical!");
    Logger.Error("Error!");
    Logger.Warning("Warning!");
    Logger.Notice("Notice!");
    Logger.Info("Info!");
    Logger.Debug("Debug!");

## Customize logging

Implement a custom logging method by annotating a plugin with the `[Log]` or `[LogDaily]` attributes.

### Configured log

Any logger configured globally can be referenced by name.

     namespace uMod.Plugins
    {
        [Info("Epic Stuff", "Unknown Author", "0.1.0")]
        [Description("Makes epic stuff happen")]
        [Log("sentry")]
        class EpicStuff : Plugin
        {
            /* ... */
        }
    }

### Default log

Specifying a custom logger will override the default logging functionality.

Send logs to the default logger _and_ a custom logger by annotating the plugin with the `[Log]` attribute and specifying `"default"` as the name.

     namespace uMod.Plugins
    {
        [Info("Epic Stuff", "Unknown Author", "0.1.0")]
        [Description("Makes epic stuff happen")]
        [Log("default")]
        class EpicStuff : Plugin
        {
            /* ... */
        }
    }

### Single file logger

Log everything to a single file unique to a plugin.

     namespace uMod.Plugins
    {
        [Info("Epic Stuff", "Unknown Author", "0.1.0")]
        [Description("Makes epic stuff happen")]
        [Log("default")]
        [Log("epic_log", "epic_stuff.log")]
        class EpicStuff : Plugin
        {
            /* ... */
        }
    }

### Daily logger

Rotate custom logging files by annotating a plugin with the `[LogDaily]` attribute and specifying a date format.

     namespace uMod.Plugins
    {
        [Info("Epic Stuff", "Unknown Author", "0.1.0")]
        [Description("Makes epic stuff happen")]
        [Log("default")]
        [LogDaily("epic_log", "epic_stuff_{date|yyyy-MM-dd}.log")]
        class EpicStuff : Plugin
        {
            /* ... */
        }
    }

TimersText Styling

### The Basics

  1. Plugins
  2. Hooks
  3. Commands
  4. Validation
  5. Configuration
  6. Localization
  7. Schematics
  8. Filesystem
  9. Preprocessors
  10. Gates
  11. Players
  12. Integration
  13. Timers
  14. **Logging**
  15. Text Styling
