# uMod - Approval Guide

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Style
  * Best practices
    * Code quality
    * Performance
    * Uniqueness
  * Undesirables

# Approval Guide

In order to ensure a plugin submission is approved, please consult the following guidelines carefully.

* * *

## Style

Most importantly, the Style Guide covers the bare minimum conventions we expect when approving a plugin.

## Best practices

### Code quality

  1. While not a requirement, cleanly formatted code is always appreciated as it makes our job easier when we can easily follow the code of plugin being submitted. Visual Studio 2015 or above is always recommended to enable support for the latest C# version as well as numerous options for helping improve your code.

  2. Using statements (i.e. `using System;` at the top of the plugin) can easily get out of hand. Try to only add what is needed by the plugin and remove those that are not. Most development environments such as Visual Studio have options and addons to handle this and more.

  3. Use static code analysis tools. After a plugin is submitted or updated, an automatic inspection checks for hundreds of potential issues and scores each discovered issue by severity. Not all issues require developer attention, but most do. Many IDE's are often bundled with static code analysis tools and offer the same or similar functionality.

  4. Do not assume anything passed to a plugin by the game, engine, or hook parameters is not null. Use null conditional operators generously.

### Performance

  1. Each hook call takes up resources and time. Some hooks are called more than others, and each may have a different performance impact depending on game mechanics or server configuration.

  2. Be mindful of how often hooks are called and, when possible, avoid implementing computationally expensive methods in frequently called hooks. Delay or distribute computation cycles, use async or cooperative multitasking, and store frequently used resources in memory.

  3. Unsubscribe from unused hooks. If a plugin only requires a specific hook in certain cases (i.e. when a particular feature is enabled), then disable that hook in all other cases.

### Uniqueness

When submitting resources to be published on uMod.org, developers must submit their own work or include express authorization from the original author(s) of the work.

While we discourage and do not accept direct copying (or forks) of code from other developers, we generally do _not_ deny plugins simply on the basis that they share any of the following components:

  1. Similar option
  2. Similar use-case
  3. Application design pattern
  4. Integration point
  5. Algorithm

## Undesirables

There are certain types of plugins that we generally do not approve. These are often because of their general use for abuse, trolling, backdoors, causing conflicts, drama, potentially problematic, or going way beyond their purpose.

  1. Piracy enabling or authentication bypassing plugins (this should be an obvious one)
  2. Any plugin attempt to bypass sandboxing or security measures put in place (security is important)
  3. Any plugin containing a backdoor or access privileges for specific individuals, even developers
  4. FPS "booster" type plugins (these have been known to be malicious and problematic)
  5. All-in-one does-everything plugins (these defeat the purpose of being modular, often cause conflicts)
  6. Copies of existing plugins with various "fixes" in them (fixes should be contributed to the original)
  7. Copies of existing plugins with messages translated directly (translations can be contributed to the original)
  8. Plugins using any sort of obfuscation method
