# uMod - Permissions

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Basic usage
  * Groups
    * Get all groups
    * Check if group exists
    * Create a group
    * Remove a group
    * Check if group has a permission
    * Grant permission to a group
    * Revoke permission from a group
    * Get the rank for a group
    * Get the title for a group
    * Get parent group for a group
    * Get permissions for a group
    * Migrate group
  * Users
    * Get permissions granted to player
    * Check if player has a permission
    * Add player to a group
    * Remove player from a group
    * Check if player is in a group
    * Grant permission to a player
    * Revoke permission from a player
  * Server
    * Get all registered permissions
    * Check if a permission exists
    * Register a permission

# Permissions

Oxide offers a substantial API to control user access with permissions and groups

## Basic usage

For a primer on how to use permissions as a server owner, please consult the _Using the Oxide permissions system_ tutorial.

Most plugins can benefit from some permissions. Below is a basic example of how to register a permission and check if a player has that permission assigned to them.

     namespace Oxide.Plugins
    {
        [Info("Epic Stuff", "Unknown Author", "0.1.0")]
        [Description("Makes epic stuff happen")]
        class EpicStuff : CovalencePlugin
        {
            private void Init()
            {
                permission.RegisterPermission("epicstuff.use", this);
            }

            private void OnUserConnected(IPlayer player)
            {
                if (player.HasPermission("epicstuff.use"))
                {
                    // Player has permission, do special stuff for them
                }
            }
        }
    }

# API

## Groups

### Get all groups

     string[] groups = permission.GetGroups();

### Check if group exists

     bool GroupExists = permission.GroupExists("GroupName");

### Create a group

     bool GroupCreated = permission.CreateGroup("GroupName", "Group Title", 0);

### Remove a group

     bool GroupRemoved = permission.RemoveGroup("GroupName");

### Check if group has a permission

     bool GroupHasPermission = permission.GroupHasPermission("GroupName", "epicstuff.use");

### Grant permission to a group

    permission.GrantGroupPermission("GroupName", "epicstuff.use", this);

### Revoke permission from a group

    permission.RevokeGroupPermission("GroupName", "epicstuff.use");

### Get the rank for a group

     int GroupRank = permission.GetGroupRank("GroupName");

### Get the title for a group

     string GroupTitle = permission.GetGroupTitle("GroupName");

### Get parent group for a group

     string GroupParent = permission.GetGroupParent("GroupName");

### Get permissions for a group

     string[] permissions = permission.GetGroupPermissions("GroupName", false);

### Migrate group

    permission.MigrateGroup("OldGroupName", "NewGroupName");

## Users

### Get permissions granted to player

     string[] UserPermissions = permission.GetUserPermissions("playerID");

### Check if player has a permission

     bool UserHasPermission = permission.UserHasPermission("playerID", "epicstuff.use");

### Add player to a group

    permission.AddUserGroup("playerID", "GroupName");

### Remove player from a group

    permission.RemoveUserGroup("playerID", "GroupName");

### Check if player is in a group

     bool UserHasGroup = permission.UserHasGroup("playerID", "GroupName");

### Grant permission to a player

    permission.GrantUserPermission("playerID", "epicstuff.use", this);

### Revoke permission from a player

    permission.RevokeUserPermission("playerID", "epicstuff.use");

## Server

### Get all registered permissions

     string[] permissions = permission.GetPermissions();

### Check if a permission exists

     bool PermissionExists = permission.PermissionExists("epicstuff.use", this);

### Register a permission

    permission.RegisterPermission("epicstuff.use", this);
