# uMod - Timers

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Single timer
  * Continuous timer
  * Repeating timer
  * Immediate timer
  * Destroying timer

# Timers

Timers generally execute functions after a set interval. Optionally continuous, repeating, and immediate timers are also available.

* * *

## Single timer

Executes a function once after the specified delay interval.

    timer.Once(1f, () =>
    {
        Puts("Hello world!");
    });

## Continuous timer

Executes a function at the specified delay interval (until the timer is manually destroyed or plugin is unloaded).

    timer.Every(3f, () =>
    {
        Puts("Hello world!");
    });

## Repeating timer

Executes a function a specific number of times at the specified delay interval. If the number of recurrences is not specified (0), then a repeating timer behaves identically to a continuous timer.

    timer.Repeat(5f, 0, () =>
    {
        Puts("Hello world!");
    });

## Immediate timer

Executes a function immediately (in the next frame).

     NextFrame(() =>
    {
        Puts("Hello world!");
    });

## Destroying timer

When a timer is no longer operating, it is marked as destroyed. Additionally timers may be destroyed manually if stored in a variable.

     Timer myTimer = timer.Every(3f, () =>
    {
        Puts("Hello world!");
    });

    myTimer.Destroy();
    if (myTimer.Destroyed)
    {
        Puts("Timer destroyed!");
    }
