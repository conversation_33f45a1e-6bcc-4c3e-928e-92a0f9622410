# uMod - Guides - Preprocessors

2 minutes to read

Created by

<PERSON><PERSON>

Updated by

<PERSON><PERSON>

#  Preprocessors

####  Using preprocessor directives

This guide is for uMod,_**not** Oxide_.

  * Introduction
  * Implementation
  * Game symbols
  * Engine symbols
  * Custom symbols

## Introduction

Preprocessor directives are lines that begin with a `#` character that provide the compiler with special instructions which change the text of the source code and result in different source code being compiled. For more information about preprocessor directives, please consider the official documentation.

## Implementation

Each game that uMod supports has an accompanying preprocessor symbol which is included in a plugin file automatically.

In a universal plugin, these symbols may be used to separate code for different games.

     private bool CanClientLogin(string clientName, string clientId, string IpAddress)
    {
    #if RUST
        // Rust-specific code
    #elif HURTWORLD
        // Hurtworld-specific code
    #endif
    }

When a plugin is compiled for Rust, only the first code will be compiled. Conversely, in a Hurtworld context, only the second code will be used.

## Game symbols

Rust|


    RUST

---|---
Hurtworld|


    HURTWORLD

7 Days To Die|


    SEVENDAYS

7 Days To Die latest_experimental|


    SEVENDAYS
    SEVENDAYSLATEST_EXPERIMENTAL

Reign Of Kings|


    REIGNOFKINGS

The Forest|


    THEFOREST

## Engine symbols

  * Unity Engine - `UNITY`
  * VRage Engine - `VRAGE`
  * XNA Framework - `XNA`

## Custom symbols

In some plugins you may see the `DEBUG` symbol or other symbols used. These are primarily for testing and debugging purposes.

In order to enable these debugging code blocks, a preprocessor directive must be defined at the beginning of the plugin file.

     #define DEBUG

    using System;

Then, elsewhere in a code...

     #if DEBUG
        Logger.Debug("Some test related info");
    #endif

FilesystemGates

### The Basics

  1. Plugins
  2. Hooks
  3. Commands
  4. Validation
  5. Configuration
  6. Localization
  7. Schematics
  8. Filesystem
  9. **Preprocessors**
  10. Gates
  11. Players
  12. Integration
  13. Timers
  14. Logging
  15. Text Styling
