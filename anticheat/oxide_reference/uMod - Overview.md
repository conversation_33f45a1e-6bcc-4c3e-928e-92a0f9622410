# uMod - Overview

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * .NET framework
  * CSharp ecosystem
  * Server-side modification
  * Plugins and hot loading
  * Extensions and products

# Overview

* * *

## .NET framework

Oxide and all of the games that Oxide actively supports are written in .NET or provide some interface through .NET.

This guide is not meant to serve as introduction to programming. Though the entry level for Oxide is low, we do assume some basic programming knowledge.

## CSharp ecosystem

Learning C# is fundamental to using Oxide. Oxide and all resources that Oxide currently supports are written in C#.

Technically Oxide has legacy support for JS, Lua, and Python plugins; however, these are currently unsupported and will likely remain so due to lack of community interest.

There are hundreds of free and open-source examples of working C# code written for Oxide and Oxide supported games.

## Server-side modification

All of the games that Oxide supports are currently multiplayer games which have both a client-side and a server-side component.

A game client is generally an executable file that a player runs locally on their machine to play the game.

In contrast, a server is generally a service running on another machine over the internet which a player connects to in order to play the game with other players.

Oxide support is injected into the server-side component exclusively. Oxide is **not** a game client modification. Our contributors are largely limited to capabilities (or lack thereof) provided by third-party game developers. This includes...

  1. Core game mechanics.
  2. Client-server integration
  3. Error handling
  4. Game engine

## Plugins and hot loading

Oxide is bundled with a C# compiler. Plugins are downloaded and installed as raw source code, and may be unloaded or loaded at a whim.

This is is especially useful during development, as Oxide will detect when plugin source code has changed and automatically reload the given plugin.

## Extensions and products

Most extensions are free and open-source binaries and must be deployed as `.dll` files. Contrary to plugins, extensions may not be hot loaded and require a server restart for every update.

Products are generally closed-source proprietary plugins or extensions which are also deployed as `.dll` files or remotely through our upcoming Marketplace extension.
