# uMod - Commands

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Chat commands
  * Console commands
  * Command permissions

# Commands

Custom commands are easily implemented with minimal boilerplate for both in-game chat interfaces and conventional command-line interfaces.

* * *

## Chat commands

Chat commands are in-game commands entered via the game client's chat, prefixed by a forward slash (/).

    [Command("test")]
    private void TestCommand(IPlayer player, string command, string[] args)
    {
        player.Reply("Test successful!");
    }

## Console commands

Console commands may be executed from the server console and in-game interfaces (where applicable).

    [Command("epicstuff.test")]
    private void TestCommand(IPlayer player, string command, string[] args)
    {
        player.Reply("Test successful!");
    }

## Command permissions

Easily restrict command usage to players who have a permission assigned to them.

    [Command("test"), Permission("epicstuff.use")]
    private void TestCommand(IPlayer player, string command, string[] args)
    {
        player.Reply("Test successful!");
    }
