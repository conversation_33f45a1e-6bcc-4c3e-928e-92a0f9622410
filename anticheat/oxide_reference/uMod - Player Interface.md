# uMod - Player Interface

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Information
    * Name
    * Id
    * Address
    * Ping
    * Language
    * IsConnected
    * IsServer
  * Administration
    * IsAdmin
    * IsBanned
    * BanTimeRemaining
    * Ban
    * Unban
    * Kick
  * Character
    * Health
    * MaxHealth
    * Heal
    * Hurt
    * Kill
    * Rename
  * Location
    * Teleport
    * Position
  * Chat and Commands
    * Message
    * Reply
    * Command
  * Permissions
    * HasPermission
    * GrantPermission
    * RevokePermission
    * BelongsToGroup
    * AddToGroup
    * RemoveFromGroup

# Player Interface

Oxide provides a standard universal method for accessing player information and performing actions on a player using the `IPlayer` interface.

* * *

For examples when to use IPlayer in practice, consider the Commands documentation.

## Information

### Name

The player's in-game display name, which by default is usually the same as their Steam account alias (but not necessarily).

     string name = player.Name;

### Id

The player's unique identification number, in many cases a 64 bit Steam ID (but not necessarily).

     string id = player.Id;

### Address

The player's IPv4 or IPv6 IP address.

     string address = player.Address;

### Ping

The player's average network ping

     int ping = player.Ping;

### Language

The player's currently configured language. For more information about `CultureInfo`, please see the official CultureInfo documentation.

     CultureInfo language = player.Language;

### IsConnected

Whether a player is currently connected to the server

     if (player.IsConnected)
    {
        player.Reply("You are connected");
    }

### IsServer

Whether a player is the server

     if (player.IsServer)
    {
        player.Reply("You are the server");
    }

## Administration

### IsAdmin

Whether a player is an administrator

     if (player.IsAdmin)
    {
        player.Reply("You are an admin");
    }

### IsBanned

Whether a player is banned from the server

     if (player.IsBanned)
    {
        player.Reply("You are banned");
    }

### BanTimeRemaining

The amount of time left before a player is unbanned (if ban is temporary). For more information about TimeSpan, please see the official TimeSpan documentation.

     TimeSpan banTimeRemaining = player.BanTimeRemaining;

### Ban

Bans a player from the server. For more information about TimeSpan, please see the official TimeSpan documentation.

    player.Ban("reason"); // Ban player indefinitely
    player.Ban("reason", new TimeSpan(2, 0, 0)); // Ban player for 2 hours

### Unban

Unbans a player, allowing them to connect to the server

    player.Unban();

### Kick

Kicks a player from the server

    player.Kick("reason");

## Character

### Health

Retrieve or update a player's health

     float health = player.Health;
    player.Health = 100f;

### MaxHealth

Retrieve or update a player's maximum health

     float maxHealth = player.MaxHealth;
    player.MaxHealth = 50f;

### Heal

Heals a player's health a given amount

    player.Heal(100f);

### Hurt

Hurts a player's health a given amount

    player.Hurt(50f);

### Kill

Kills a player, causing them to die

    player.Kill();

### Rename

Renames a player, changing their in-game name

    player.Rename("EpicName");

## Location

### Teleport

Teleports a player to the given world position

     float x = 1;
    float y = 2;
    float z = 3;
    player.Teleport(x, y, z);

    GenericPosition position = new GenericPosition(x, y, z);
    player.Teleport(position);

### Position

Retrieve a player's in-game character position

     GenericPosition position = player.Position();

## Chat and Commands

### Message

Sends the given message and prefix to a player

    player.Message("hello world");

### Reply

Replies to a player with the given message and prefix

    player.Reply("hello world");

### Command

Runs the given console command as a player

    player.Command("command", arg1, arg2 /* , ... */ );

## Permissions

### HasPermission

Checks if a player has the given permission

     if (player.HasPermission("epicstuff.use"))
    {
        player.Reply("You have the epic permission");
    }

### GrantPermission

Grants a given permission to a player

    player.GrantPermission("epicstuff.use");

### RevokePermission

Removes a given permission from a player

    player.RevokePermission("epicstuff.use");

### BelongsToGroup

Checks if a player belongs to a given group

     if (player.BelongsToGroup("admin"))
    {
        player.Reply("You are in the admin group");
    }

### AddToGroup

Adds a player to a given group

    player.AddToGroup("admin");

### RemoveFromGroup

Removes a player from a given group

    player.RemoveFromGroup("admin");
