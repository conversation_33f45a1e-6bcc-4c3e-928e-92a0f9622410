# uMod - Hooks

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Basic example
  * Available hooks
  * Custom hooks
    * Calling a hook from a plugin reference
    * Calling a hook globally
    * Hook subscription
    * Unsubscribe from hook
    * Subscribe to hook
    * Hook overloading

# Hooks

uMod supports _hundreds_ of hooks and even more are available through plugins and extensions.

Hooks are what make a plugin tick in most cases. A hook event is triggered every time a feature aspect of the game (or injection site) is procedurally passed through. A hook, once triggered, will call all plugin methods that are subscribed to it.

More generally a hook is a default server-side game behavior which a plugin tracks, modifies, augments, or cancels.

* * *

### Basic example

     private bool CanUserLogin(string name, string id, string ip)
    {
        Puts("No one can connect");
        return false; // By returning false, no players may connect to this server
    }

## Available hooks

Please consult our Games documentation for a list of all available hooks.

## Custom hooks

Developers may leverage hooks to easily develop integrations between plugins.

### Calling a hook from a plugin reference

For information about plugin references, please consult dependencies

     bool result = EpicStuff.CallHook<bool>("CanDoSomething");
    if (result)
    {
        Puts("Player can do the thing!");
    }

### Calling a hook globally

     bool result = Interface.CallHook<bool>("CanDoSomething");
    if (result)
    {
        Puts("Player can do the thing!");
    }

### Hook subscription

By default, hooks that are included with uMod will be subscribed to automatically by a plugin when the plugin loads. A developer does not need to do anything special to subscribe to a hook aside from write a hook method that has the correct method name and corresponding parameters.

However, developers may exercise direct control over which hooks are used by unsubscribing or subscribing to hooks manually.

It is recommended for many (CPU intensive) hooks to unsubscribe from them entirely when they are not in use by any feature of a plugin.

#### Unsubscribe from hook

This example will unsubscribe `OnUserChat` if `chatFeatureEnabled` is `false`.

     bool chatFeatureEnabled = false;

    void Init()
    {
        if (!chatFeatureEnabled)
        {
            Unsubscribe("OnUserChat");
        }
    }

    object OnUserChat(IPlayer player, string message)
    {
        // Do stuff
    }

#### Subscribe to hook

After unsubscribing from a hook as in the previous example, the plugin may re-subscribe again to re-enable the plugin's chat behavior.

    [Command("test")]
    private void TestCommand(IPlayer player, string command, string[] args)
    {
        if (player.IsAdmin)
        {
            Puts("Enabling chat behavior");
            Subscribe("OnUserChat");
        }
    }

### Hook overloading

Hooks support method overloading. This means that, if possible, a hook will call the method which has a signature that most closely resembles the arguments provided (when the methods have the same name).

     int EpicNumber = 42;
    string EpicString = "Whoa";

    Interface.CallHook("EpicHook", EpicNumber);
    Interface.CallHook("EpicHook", EpicNumber, EpicString);

To catch the above hooks:

     private void EpicHook(int epicNumber)
    {
        Puts($"Received a number '{epicNumber}'");
    }

    private void EpicHook(int epicNumber, string epicString)
    {
        Puts($"Received a number '{epicNumber}' AND string '{epicString}'");
    }
