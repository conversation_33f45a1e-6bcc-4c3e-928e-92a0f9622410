# uMod - Security

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Sandbox
    * Restricted namespaces
    * Restriction exceptions
  * Extensions

# Security

* * *

## Sandbox

Plugins and products are compiled in a restricted mode that prevents a substantial number of .NET features from being used.

These restrictions are in place to prevent potentially malicious code.

### Restricted namespaces

The list of namespaces restricted by the sandbox includes, but is not limited to:

  1. `System.IO`
  2. `System.Net`
  3. `System.Reflection`
  4. `System.Threading`
  5. `System.Runtime.InteropServices`
  6. `System.Diagnostics`
  7. `System.Security`
  8. `System.Timers`

### Restriction exceptions

There are some exceptions to the above list, and they are:

  1. `System.Diagnostics.Stopwatch`
  2. `System.IO.MemoryStream`
  3. `System.IO.Stream`
  4. `System.IO.BinaryReader`
  5. `System.IO.BinaryWriter`
  6. `System.Net.Dns`
  7. `System.Net.Dns.GetHostEntry`
  8. `System.Net.IPAddress`
  9. `System.Net.IPEndPoint`
  10. `System.Net.NetworkInformation`
  11. `System.Net.Sockets.SocketFlags`
  12. `System.Security.Cryptography`
  13. `System.Threading.Interlocked`

## Extensions

Extensions are generally not sandboxed, meaning that any code deployed as an extension will have unmitigated access to all .NET libraries.

For this reason, we do not generally accept extensions except in the most needful cases. If code is submitted as an extension, the author ought to be prepared to demonstrate why it _absolutely must_ be deployed as an extension.
