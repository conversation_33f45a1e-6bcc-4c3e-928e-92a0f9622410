# uMod - Guides - Gates

7 minutes to read

Created by

<PERSON><PERSON>

Updated by

<PERSON><PERSON>

#  Gates

####  Details about basic gate usage

This guide is for uMod,_**not** Oxide_.

  * Introduction
  * Simple gate
  * Configurated gate
  * Hook gate
    * Allows
    * Denies
  * Aggregate policy
    * Any
    * All
    * None
  * Permission gate
    * Registering permissions
    * Assigning permissions
    * Allows
    * Denies
  * Default groups

## Introduction

A **Gate** is a container of possible actions that require authorization to control player access to a feature or set of features.

## Simple gate

The following examples are naive demonstrations of how to use the gate system with a hypothetical hook `OnJump`.

     class MyJumpGate : uMod.Auth.Gate
    {
        public MyJumpGate(IPlugin plugin) : base(plugin)
        {
        }

        public bool Jumping(IPlayer player)
        {
            if (player.Name == "Calytic")
            {
                // Cal<PERSON> cannot jump
                return false;
            }

            // Everyone else can jump
            return true;
        }
    }

The gate method definition (e.g. `MyJumpGate.Jumping`) must accept an `IPlayer` as the first parameter and return a boolean as above.

Implement a hook, for example named `OnJump`, and check whether or not the player should be allowed to jump.

     bool OnJump(IPlayer player, MyJumpGate jumpGate)
    {
        if (jumpGate.Allows(nameof(MyJumpGate.Jumping), player))
        {
            // Jumping was allowed
            return true;
        }

        // Jumping was not allowed
        return false;
    }

Gate policy names are case in-sensitive and the player parameter is required.

Any gate implementations within a plugin will be created automatically when a plugin loads and are available for injection in hooks via the service container.

## Configurated gate

A configurated gate has policies that rely on a configuration file.

The following example uses a configuration schematic to determine the `Jumping` policy.

    [Config]
    class MyConfig
    {
        public bool JumpingAllowed = true;
    }

    class MyJumpGate : uMod.Auth.Gate
    {
        MyConfig myConfig;

        public MyJumpGate(IPlugin plugin, MyConfig myConfig) : base(plugin)
        {
            this.myConfig = myConfig;
        }

        public bool Jumping(IPlayer player)
        {
            return myConfig.JumpingAllowed;
        }
    }

    bool OnJump(IPlayer player, MyJumpGate jumpGate)
    {
        return jumpGate.Jumping(player);
    }

The `MyJumpGate` constructor above will use dependency injection to inject the `MyConfig` configuration schematic once it is loaded.

## Hook gate

Allow or deny access to a hook by annotating the hook method with the `Allows` or `Denies` attribute.

### Allows

The player _must_ be authorized by `MyJumpGate.Jumping`.

    [Hook("OnJump")]
    [Allows(typeof(MyJumpGate), nameof(MyJumpGate.Jumping))]
    bool OnJumpAllowed(IPlayer player)
    {
        player.Reply("You jumped successfully!");
        return true;
    }

### Denies

The player _must not_ be authorized by `MyJumpGate.Jumping`.

    [Hook"OnJump")]
    [Denies(typeof(MyJumpGate), nameof(MyJumpGate.Jumping))]
    bool OnJumpDenied(IPlayer player)
    {
        player.Reply("You cannot jump, try working out!");
        return false;
    }

## Aggregate policy

Authorize a hook against multiple policies by annotating a hook method with the `Any`, `All`, or `None` attributes.

For example, imagine a gate `MyActionGate` that implements two policies: `jumping` and `flying`. The following snippets demonstrate an aggregate policy.

### Any

Example: `jumping` OR `flying` allowed

     // Either jumping OR flying is allowed
    [Any(typeof(MyActionGate), nameof(MyJumpGate.Jumping), nameof(MyJumpGate.Flying))]
    bool OnJump(IPlayer player)
    {
        player.Reply("You jumped successfully");
        return true;
    }

### All

Example: `jumping` AND `flying` allowed

    [All(typeof(MyActionGate), nameof(MyJumpGate.Jumping), nameof(MyJumpGate.Flying))]
    bool OnJump(IPlayer player)
    {
        player.Reply("You jumped successfully");
        return true;
    }

### None

Example: `jumping` OR `flying` disallowed

     // Neither jumping or flying is allowed
    [None(typeof(MyActionGate), nameof(MyJumpGate.Jumping), nameof(MyJumpGate.Flying))]
    bool OnJump(IPlayer player)
    {
        player.Reply("You cannot jump or fly, try growing wings!");
        return false;
    }

## Permission gate

A permission gate combines the permission system with the Gate system.

### Registering permissions

Every permission must be registered before it can be assigned, thus it is typical to register permissions early in the `Loaded` hook.

     void Loaded()
    {
        Gate.Register("use_feature");
    }

### Assigning permissions

All permissions are automatically prefixed with the name of the plugin that registered them.

For example: `pluginname.use_feature`

Permissions can be granted to an individual player or groups. It is recommended to grant permissions to groups and add players to the groups; _not_ assign permissions to players directly.

Grant permissions to a group and add a player to that group via authorization commands or the API.

Grant a permission directly to the player via authorization commands or the API.

### Allows

Use the built-in permission gate to check if a player is allowed a specific permission.

     void OnPlayerConnect(IPlayer player)
    {
        if (Gate.Allows("use_feature", player))
        {
            // Player is allowed to use feature
        }
    }

Annotat a hook method with the `Allows` attribute to ensure the hook method is invoked only when the player is allowed the specified permission.

    [Allows("use_feature")]
    void OnPlayerConnect(IPlayer player)
    {
        // Player is allowed to use feature
    }

For the above code to work, the connecting player must have the `pluginname.use_feature` permission.

### Denies

Use the built-in permission gate to check if a player is denied a specific permission.

     void OnPlayerConnect(IPlayer player)
    {
        if (Gate.Denies("use_feature", player))
        {
            // Player is *not* allowed to use feature
        }
    }

Annotat a hook method with the `Denies` attribute to ensure the hook method is invoked only when the player is denied a specific permission.

    [Denies("use_feature")]
    void OnPlayerConnect(IPlayer player)
    {
        // Player is *not* allowed to use feature
    }

## Default groups

Some games provide authentication methods outside of uMod to grant special privileges. uMod will automatically assign the default groups: `admin` and `moderators` where appropriate.

When a player is initialized after joining the server for the first time, they are automatically added to the `default` group. It is possible to customize the default group names, namely `default`, `moderators`, and `admin` in the `config/auth.toml` file.

PreprocessorsPlayers

### The Basics

  1. Plugins
  2. Hooks
  3. Commands
  4. Validation
  5. Configuration
  6. Localization
  7. Schematics
  8. Filesystem
  9. Preprocessors
  10. **Gates**
  11. Players
  12. Integration
  13. Timers
  14. Logging
  15. Text Styling
