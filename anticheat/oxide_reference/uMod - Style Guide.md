# uMod - Style Guide

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Title
  * Author
  * Version
  * Description

# Style Guide

Plugins and extensions require some core scaffolding to be functional and/or approved within the uMod ecosystem.

* * *

## Title

The title of the plugin (first part of Info attribute in C# plugins) must be set and closely match the submission name on our site. Please avoid using "Plugin" or "uMod" in the title as that would be a bit redundant.

Good Ban System

Bad BanPlugin for Admin

## Author

The author of the plugin (second part of Info attribute in C# plugins) must be set and match or contain the name of the user submitting the plugin on our site. Please do not use this to advertise websites or game servers, or anything really.

Good

#### Wulf

Bad

#### Fluw @ MyServer.net

## Version

The version of the plugin (third part of Info attribute in C# plugins) must be set in the x.x or "x.x.x" format. This should be updated each time a plugin update is released. Semantic Versioning is recommended, though not fully supported.

Good 1.2.3

Bad 2017-02-01 Beta 1

## Description

The Description is an optional, standalone attribute, but recommended as other plugins can utilize it for making help commands and such. Please make sure to actually describe the plugin, but keep it brief. Best practice is to use Sentence case, not Title Case or CAPS. Keep it clean!

Good Allows admin to ban players easily on command

Bad Cool Admin BanPlugin
