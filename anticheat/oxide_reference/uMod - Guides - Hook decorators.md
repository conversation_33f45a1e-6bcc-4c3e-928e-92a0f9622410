# uMod - Guides - Hook decorators

2 minutes to read

Created by

Calytic

Updated by

Wulf

#  Hook decorators

####  Scaffold multiple hooks at once with hook decorators

This guide is for uMod,_**not** Oxide_.

  * Introduction
  * Manual subscription
  * Automatic registration
  * Decorate dependency

## Introduction

A hook decorator is a group of hooks that are defined together. By subscribing or unsubscribing to/from a hook decorator, multiple hooks which work together can be neatly organized with their own state information and enabled or disabled all at once.

    [HookDecorator]
    class MyHookDecorator : HookDecorator
    {
        List<IPlayer> ConnectedPlayers = new List<IPlayer>();

        public MyDecorator(Plugin plugin) : base(plugin)
        {
        }

        [Hook("OnPlayerConnect")]
        private void OnPlayerConnect(IPlayer player)
        {
            ConnectedPlayers.Add(player);
        }

        [Hook("OnPlayerDisconnect")]
        private void OnPlayerDisconnect(IPlayer player)
        {
            ConnectedPlayers.Remove(player);
        }
    }

## Manual subscription

Subscribe to hooks within hook decorator.

     Subscribe(nameof(MyHookDecorator));

Unsubscribe hooks within a hook decorator.

     Unsubscribe(nameof(MyHookDecorator));

Check if a hook decorator is subscribed.

     IsSubscribed(nameof(MyHookDecorator));

## Automatic registration

By default, hooks within a hook decorator are automatically registered and subscribed. Disable automatic registration of decorated hooks by setting `AutoRegister` to `false`.

    [HookDecorator(AutoRegister = false)]
    class MyHookDecorator : HookDecorator
    {
        /* ... */
    }

## Decorate dependency

Implement a required dependency and annotate the dependent plugin with a hook decorator supplied from the dependency.

> umod/plugins/RequiredPlugin.cs

    [Info("Required Plugin", "uMod", "1.0.0")]
    public class RequiredPlugin : Plugin
    {
        [HookDecorator]
        public class MyHookDecorator : HookDecorator
        {
            /* ... */
        }
    }

> umod/plugins/DependentPlugin.cs

    [Info("Dependent Plugin", "uMod", "1.0.0")]
    [HookDecorator(typeof(RequiredPlugin.MyHookDecorator))]
    public class DependentPlugin : Plugin
    {
        [Requires]
        RequiredPlugin RequiredPlugin;
    }

Duck typingPromises

### Advanced

  1. Hook events
  2. Web requests
  3. Pooling
  4. Duck typing
  5. **Hook decorators**
  6. Promises
  7. Database
  8. Modeling
