# uMod - Guides - Schematics

5 minutes to read

Created by

<PERSON><PERSON>

Updated by

<PERSON><PERSON>

#  Schematics

####  Details about implementing schematics for JSON, TOML, or Binary files

This guide is for uMod,_**not** Oxide_.

  * Introduction
  * JSON
  * TOML
  * Binary
  * Attribute names
    * JSON Attribute Names
    * TOML attribute names
  * Ignored members
    * JSON ignored members
    * TOML ignored members
  * Comments
    * TOML comments
  * Nesting
    * JSON nesting
    * TOML nesting
  * Multi-line strings
    * TOML multi-line strings

## Introduction

A schematic is a class that defines the shape of a file or message. The properties and fields of the schematic serve as blueprint to serialize and deserialize objects to/from intermediate formats such as `JSON`, `TOML` or `Binary`.

## JSON

     class MySchematic
    {
        public bool Feature = true;
        public int Distance = 10;
    }

The above schematic may be used for a configuration file, localization file, or data file by annotating it with the appropriate attributes.

By default, schematics will be serialized as Javascript Object Notiation (`JSON`).

    {
        "Feature" : true,
        "Distance": 10
    }

## TOML

Annotate a schematic with the `[Toml]` attribute to serialize the schematic to the Tom's Obvious Markup Language (`TOML`)

    [Toml]
    class MySchematic
    {
        public bool Feature = true;
        public int Distance = 10;
    }

     Feature = true
    Distance = 10

## Binary

Annotate a schematic with the `[Binary]` attribute to serialize the schematic to binary. Binary is not a human-readable format, thus the file will be serialized as a sequence of numbers.

    [Binary]
    class MySchematic : IBinaryObject
    {
        public bool Sleeping = true;
        public float Distance = 10;

        void Write(BinaryWriter writer)
        {
            writer.Write(Sleeping);
            writer.Write(Distance);
        }

        void Read(BinaryReader reader)
        {
            Sleeping = reader.ReadBoolean();
            Distance = reader.ReadSingle();
        }
    }

Implementing the `IBinaryObject` interface is optional but it is recommended because it will improve the performance of serialization.

## Attribute names

Annotate schematic fields or schematic properties with the `[JsonProperty]` or `[TomlProperty]` attribute to customize the name of the field or property in the intermediate format.

### JSON Attribute Names

     class MySchematic
    {
        [JsonProperty("feature")]
        public bool Feature = true;
        [JsonProperty("distance")]
        public int Distance = 10;
    }

    {
        "feature" : true,
        "distance": 10
    }

### TOML attribute names

    [Toml]
    class MySchematic
    {
        [TomlProperty("feature")]
        public bool Feature = true;
        [TomlProperty("distance")]
        public int Distance = 10;
    }

     feature = true
    distance = 10

Spaces should not be used in attribute names. Use underscores instead.

## Ignored members

Annotate a schematic field or property with the `[JsonIgnore]` or `[TomlIgnore]` attribute to prevent the field or property from being serialized.

### JSON ignored members

     class MySchematic
    {
        public bool Feature = true;
        [JsonIgnore]
        public int Distance = 10;
    }

    {
        "Feature" : true,
    }

### TOML ignored members

    [Toml]
    class MySchematic
    {
        public bool Feature = true;
        [TomlIgnore]
        public int Distance = 10;
    }

     Feature = true

## Comments

Annotate a schematic field or property with the `[TomlComment]` attribute. `JSON` files do not support comments.

### TOML comments

    [Toml]
    class MySchematic
    {
        [TomlComment("Feature comment")]
        public bool Feature = true;
        public int Distance = 10;
    }

     # Feature comment
    Feature = true
    Distance = 10

Alternatively, comments may be specified in `[TomlProperty]` attribute to the same effect..

    [Toml]
    class MySchematic
    {
        [TomlProperty("feature", Comment = "Feature comment")]
        public bool Feature = true;
        [TomlProperty("distance")]
        public int Distance = 10;
    }

## Nesting

Nested objects are automatically handled without any additional annotations.

### JSON nesting

     class MySchematicFeature
    {
        public bool Enabled = true;
    }

    class MySchematic
    {
        public MySchematicFeature Feature = new MySchematicFeature;
        public int Distance = 10;
    }

    {
        "Feature" : {
            "Enabled" : true
        },
        "Distance" : 10
    }

### TOML nesting

     class MySchematicFeature
    {
        public bool Enabled = true;
    }

    [Toml]
    class MySchematic
    {
        public MySchematicFeature Feature = new MySchematicFeature;
        public int Distance = 10;
    }

     Distance = 10

    [Feature]
    Enabled = true

## Multi-line strings

### TOML multi-line strings

Annotate a schematic with the `[TomlProperty]` attribute and specify the `MultiLine` option as `true`.

    [Toml]
    class MySchematic
    {
        [TomlProperty(MultiLine = true)]
        public string MyString = @"greeting
        world";
    }

     MyString = """greeting
    location"""

LocalizationFilesystem

### The Basics

  1. Plugins
  2. Hooks
  3. Commands
  4. Validation
  5. Configuration
  6. Localization
  7. **Schematics**
  8. Filesystem
  9. Preprocessors
  10. Gates
  11. Players
  12. Integration
  13. Timers
  14. Logging
  15. Text Styling
