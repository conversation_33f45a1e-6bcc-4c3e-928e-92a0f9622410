# uMod - Guides - Filesystem

5 minutes to read

Created by

<PERSON><PERSON>

Updated by

Wulf

#  Filesystem

####  Basic data file handling

This guide is for uMod,_**not** Oxide_.

  * File schematic
    * Alternative formats
  * Concurrency
  * Serializing data
  * Deserializing data
  * Batch operations
  * Data files
    * Create file
    * Create file with default value
    * Create file with existing value
    * Create file with callback
    * Save file
    * Load file
    * Check file existence
    * Delete file
  * Directory helpers
    * Check file existence
    * Delete files
    * Get files

A data file is a file which stores arbitrary data to be used by a plugin.

## File schematic

A schematic is a class which represents the shape of the data file. By default, data schematics will be serialized as `JSON` on the filesystem unless otherwise specified. `TOML` and `Binary` are also supported with the file extensions `.toml` and `.dat`, respectively.

     class MyData
    {
        public bool Sleeping = true;
        public Position LastPosition;
    }

### Alternative formats

Files may also be serialized as TOML or binary.

## Concurrency

To minimize the impact of expensive I/O operations; read/write/batch operations are asynchronous (or non-blocking) by default. Promises are used to asynchronously read and write data on a background thread.

## Serializing data

Serialization is the process of transforming the data schematic into a format that can be stored in a file.

     MyData myData = new MyData();
    myData.LastPosition = new Position(10, 10, 0);
    Files.WriteObject("myData", myData).Fail(Logger.Report);

> umod/data/MyPlugin/myData.json

    {
        "Sleeping" : true,
        "LastPosition" : {
            "X" : 10,
            "Y" : 10,
            "Z" : 0
        }
    }

## Deserializing data

Deserialization is the process of transforming serialized data on the filesystem back into an in-memory object.

    Files.ReadObject<MyData>("myData")
        .Done(delegate(MyData myData)
        {
            Logger.Info($"Position: {myData.LastPosition}");
            // Prints: Position: (10, 10, 0)
        },
        delegate(Exception exception)
        {
            Logger.Report("Could not load file", exception);
        });

## Batch operations

Perform multiple file operations at once using `WriteObjects` and `ReadObjects`.

    Dictionary<string, MyData> writeBatch = new Dictionary<string, MyData>();

    writeBatch.Add("myData_1", new MyData());
    writeBatch.Add("myData_2", new MyData());

    Files.WriteObjects(writeBatch).Fail(Logger.Report);

Reading operations resolve to an `IEnumerable<MyData>` object.

    Files.ReadObjects(new[]
    {
        "myData_1",
        "myData_2"
    })
    .Done(delegate(IEnumerable<MyData> data)
    {
        Logger.Info($"Read data {data.Count()}");
    }, Logger.Report)

## Data files

The above serialization methods are provided by the `IDataFile<T>` interface which may be used directly for more control over data files.

### Create file

#### Create file with default value

Create a data file object with a default value that does not exist on filesystem until saved

    IDataFile<MyData> Files.GetDataFile<MyData>("myData_1", true);

#### Create file with existing value

Create a data file object with a default value that does not exist on filesystem until saved

     MyData myData = new MyData();
    IDataFile<MyData> Files.GetDataFile<MyData>("myData_1", myData);

#### Create file with callback

Create a data file object that populates a default value using the specified callback.

    IDataFile<MyData> Files.GetDataFile<MyData>("myData_1", delegate() {
        return new MyData();
    });

     MyData GetDefaultMyData()
    {
        return new MyData();
    }

    IDataFile<MyData> Files.GetDataFile<MyData>("myData_1", GetDefaultMyData);

### Save file

    IDataFile<MyData> myDataFile = Files.GetDataFile<MyData>("myData_1", delegate() {
        return new MyData();
    });
    myDataFile.SaveAsync().Fail(Logger.Report);

### Load file

    myDataFile.LoadAsync()
        .Done(delegate(MyData myData)
        {
              Logger.Info("Data file loaded!");
        },
        Logger.Report);

### Check file existence

Check if the file already exists

     if (myDataFile.Exists)
    {
        Logger.Info($"File exists: {myDataFile.Path}");
    }

### Delete file

Delete the file

    myDataFile.Delete();

## Directory helpers

Check for the existence of files, delete files, or list files without creating data objects.

### Check file existence

Check if a file exists.

     if (Files.Exists("myData.json"))
    {
        Logger.Info("myData.json exists!");
    }

### Delete files

Delete a single file to multiple files which match the specified pattern.

    Files.Delete("*.json")

### Get files

Get a list of files that match the specified pattern.

     foreach(string file in Files.GetFiles("*.json"))
    {
        Logger.Info($"{file} found!");
    }

SchematicsPreprocessors

### The Basics

  1. Plugins
  2. Hooks
  3. Commands
  4. Validation
  5. Configuration
  6. Localization
  7. Schematics
  8. **Filesystem**
  9. Preprocessors
  10. Gates
  11. Players
  12. Integration
  13. Timers
  14. Logging
  15. Text Styling
