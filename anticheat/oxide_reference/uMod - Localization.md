# uMod - Localization

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Localization guidelines
  * Plugin messages
    * Registering messages
    * Get all messages
    * Get a single message
    * Formatting a message
  * Player language
    * Get player language
    * Set player language
  * Plugin languages
    * Get plugin languages
  * Server language
    * Get server language
    * Set server language

# Localization

Oxide provides a simple API with which a developer may easily add support for multiple languages to plugins and extensions.

* * *

## Localization guidelines

  1. English, please
While we certainly encourage submissions that support multiple languages, English _must_ be included.

  2. No prefixes
While not required, not prefixing messages with your plugin name is generally encouraged as it keeps the messages sent to players shorter and cleaner. Some plugin developers opt for having the prefix configurable via the configuration file, but most do not set one.

  3. Format
Try to keep messages brief and to the point to avoid taking up an excess amount of screen or console space. Best practice is to use Sentence case, not Title Case or ALL CAPS.

## Plugin messages

### Registering messages

When a plugin loads, it must register all of the messages used in the plugin.

     protected override void LoadDefaultMessages()
    {
        lang.RegisterMessages(new Dictionary<string, string>
        {
            ["EpicThing"] = "An epic thing has happened",
            ["EpicTimes"] = "An epic thing has happened: {0} time(s)"
        }, this);
    }

### Get all messages

Retrieves a Dictionary of all the messages registered for a particular plugin and language.

    Dictionary<string, string> messages = lang.GetMessages("en", this);

    Puts($"Messages for {Title}:");
    foreach(KeyValuePair<string, string> message in messages)
    {
        Puts($"{message.Key}: {message.Value}");
    }

### Get a single message

    [Command("epicstuff.message")]
    private void TestMessageCommand(IPlayer player)
    {
        string message = lang.GetMessage("EpicThing", this, player.Id);
        Puts(message);
    }

###### The player's ID is passed to the `GetMessage` method, which will send them the message in their language when available.

### Formatting a message

     int amount = 0;

    [Command("epicstuff.amount")]
    private void TestAmountCommand(IPlayer player)
    {
        amount++;
        string message = lang.GetMessage("EpicTimes", this, player.Id);
        Puts(string.Format(message, amount.ToString()));
    }

## Player language

### Get player language

Retrieve the server-wide language setting for a player.

    [Command("epicstuff.language")]
    private void TestLanguageCommand(IPlayer player)
    {
        Puts(lang.GetLanguage(player.Id));
        // Will output (by default): en
    }

### Set player language

Update the server-wide language setting for a player.

    [Command("epicstuff.french")]
    private void TestUpdateCommand(IPlayer player)
    {
        lang.SetLanguage("fr", player.Id);
        Puts("Merci bien! Votre langue est le français");
    }

Players can also set their language by using the included `oxide.lang`, `o.lang`, or `lang` console or chat commands along with their desired, available two-letter language code.

## Plugin languages

### Get plugin languages

Retrieves an array of strings containing a list of all the languages that a plugin supports.

     string[] languages = lang.GetLanguages(this);

    Puts($"Supported languages for {Title}:");
    foreach(string language in languages)
    {
        Puts(language);
    }

## Server language

### Get server language

Retrieve the default language for the server.

     Puts(lang.GetServerLanguage()); // Will output (by default): en

### Set server language

Update the default language for the server.

    lang.SetServerLanguage("fr"); // Will set language to "fr"
