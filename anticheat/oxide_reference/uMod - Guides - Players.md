# uMod - Guides - Players

12 minutes to read

Created by

Calytic

Updated by

<PERSON><PERSON>

#  Players

####  Interacting with the universal player abstraction

This guide is for uMod,_**not** Oxide_.

  * Introduction
  * Information
    * Name
    * Id
    * Address
    * Ping
    * Language
    * IsConnected
    * IsServer
  * Administration
    * IsAdmin
    * IsBanned
    * BanTimeRemaining
    * Ban
    * Unban
    * Kick
  * Character
    * Health
    * MaxHealth
    * Heal
    * Hurt
    * Kill
    * Rename
  * Location
    * Teleport
    * Position
    * Unity vectors
  * Chat and commands
    * Message
    * Reply
    * Command
  * Permissions
    * HasPermission
    * GrantPermission
    * RevokePermission
    * BelongsToGroup
    * AddToGroup
    * RemoveFromGroup
  * Player manager
    * FindPlayer
    * PlayerFilter
    * FindPlayers
    * FindPlayerById
    * FindPlayerByObj
    * GamePlayer.IPlayer Property
    * IPlayer.Object property
  * IPlayer and "GamePlayer" substitution
  * OnlinePlayers decorator
    * OnlinePlayers OnConnect/OnDisconnect
    * OnlinePlayers file persistence

## Introduction

Different games use wildly different abstractions to represent players internally. uMod standardizes these varying implementations by providing a universal `IPlayer` interface which represents a player in any game that uMod supports.

## Information

### Name

The player's in-game display name, which by default is usually the same as their Steam account alias (but not necessarily).

> Signature

     string Name { get; }

> Implementation

    Logger.Info(player.Name);

### Id

The player's unique identification number, in many cases a 64-bit Steam ID (but not necessarily).

> Signature

     string Id { get; }

> Implementation

    Logger.Info(player.Id);

### Address

The player's IPv4 or IPv6 IP address.

> Signature

     string Address { get; }

> Implementation

    Logger.Info(player.Address);

### Ping

The player's average network ping.

> Signature

     int Ping { get; }

> Implementation

    Logger.Info(player.Ping);

### Language

The player's currently configured language. For more information about CultureInfo, please see the official CultureInfo documentation.

> Signature

     CultureInfo Language { get; }

> Implementation

    Logger.Info(player.Language.DisplayName);

### IsConnected

Determine whether a player is currently connected to the server.

> Signature

     bool IsConnected { get; }

> Implementation

     if (player.IsConnected)
    {
        player.Reply("You are connected");
    }

### IsServer

Determine whether a player is the server.

> Signature

     bool IsServer { get; }

> Implementation

     if (player.IsServer)
    {
        player.Reply("You are the server");
    }

## Administration

### IsAdmin

Determine whether a player is an administrator.

> Signature

     bool IsAdmin { get; }

> Implementation

     if (player.IsAdmin)
    {
        player.Reply("You are an admin");
    }

### IsBanned

Determine whether a player is banned from the server.

> Signature

     bool IsBanned { get; }

> Implementation

     if (player.IsBanned)
    {
        Logger.Info("Banned player tried to connect");
    }

### BanTimeRemaining

The amount of time left before a player is unbanned (if ban is temporary). For more information about TimeSpan, please see the official TimeSpan documentation.

> Signature

     TimeSpan BanTimeRemaining { get; }

> Implementation

     if (player.IsBanned)
    {
        Logger.Info($"Banned player ({player.Name}) attempted to connect but is banned for {player.BanTimeRemaining.TotalDays} days");
    }

### Ban

Bans a player from the server. For more information about TimeSpan, please see the official TimeSpan documentation.

> Signature

     void Ban(string reason, TimeSpan duration = null)

> Implementation

    player.Ban("reason"); // Ban player indefinitely
    player.Ban("reason", new TimeSpan(2, 0, 0)); // Ban player for 2 hours

### Unban

Unbans a player, allowing them to connect to the server again.

> Signature

     void Unban()

> Implementation

    player.Unban();

### Kick

Kicks a player from the server.

> Signature

     void Kick(string reason)

> Implementation

    player.Kick();

## Character

### Health

Retrieve or update a player's health.

> Signature

     float Health { get; set; }

> Implementation

     float health = player.Health;
    player.Health = 100f;

### MaxHealth

Retrieve or update a player's maximum health.

> Signature

     float MaxHealth { get; set; }

> Implementation

     float maxHealth = player.MaxHealth;
    player.MaxHealth = 50f;

### Heal

Heals a player's health a given amount.

> Signature

     void Heal(float amount);

> Implementation

    player.Heal(100f);

### Hurt

Hurts a player's health a given amount.

> Signature

     void Hurt(float amount);

> Implementation

    player.Hurt(100f);

### Kill

Kills a player, causing them to die.

> Signature

     void Kill();

> Implementation

    player.Kill();

### Rename

Renames a player, changing their in-game name.

> Signature

     void Rename(string name);

> Implementation

    player.Rename("EpicName");

## Location

### Teleport

Teleports a player to the given world position.

> Signature

     void Teleport(float x, float y, float z);
    void Teleport(Position position);

> Implementation

    player.Teleport(1, 2, 3);
    player.Teleport(new Position (1, 2, 3 ));

### Position

Retrieve a player's in-game character position

> Signature

     void Position();

> Implementation

     Position position = player.Position();

#### Unity vectors

The universal vectors `Point`, `Position`, and `Position4` can all be converted to and from their Unity counterparts with the following extension methods..

    Vector2.ToPoint();
    Point.ToVector2();
    Vector3.ToPosition();
    Position.ToVector3();
    Vector4.ToPosition4();
    Position4.ToVector4();

## Chat and commands

### Message

Sends the given message and prefix to a player.

> Signature

     void Message(string message, string prefix = null, params object[] args)

> Implementation

    player.Message("hello world");

### Reply

Sends the given message and prefix to a player.

> Signature

     void Reply(string message, string prefix = null, params object[] args = null)

> Implementation

    player.Reply("hello world");

### Command

Runs the given console command as a player.

> Signature

     void Command(string command, params object[] args = null)

> Implementation

    player.Command("command", arg1, arg2 /* , ... */ );

## Permissions

### HasPermission

Checks if a player has the given permission.

> Signature

     bool HasPermission(string permission)

> Implementation

     if (player.HasPermission("epicstuff.use"))
    {
        player.Reply("You have the epic permission");
    }

### GrantPermission

Grants a given permission to a player.

> Signature

     void GrantPermission(string permission)

> Implementation

    player.GrantPermission("epicstuff.use");

### RevokePermission

Removes a given permission from a player.

> Signature

     void RevokePermission(string permission)

> Implementation

    player.RevokePermission("epicstuff.use");

### BelongsToGroup

Checks if a player belongs to a given group.

> Signature

     bool BelongsToGroup(string groupName)

> Implementation

     if (player.BelongsToGroup("admin"))
    {
        player.Reply("You are in the admin group");
    }

### AddToGroup

Adds a player to a given group.

> Signature

     void AddToGroup(string groupName)

> Implementation

    player.AddToGroup("admin");

### RemoveFromGroup

Removes a player from a given group.

> Signature

     void RemoveFromGroup(string groupName)

> Implementation

    player.RemoveFromGroup("admin");

## Player manager

To find an instance of an arbitrary player, the IPlayerManager is available by default in all plugins.

### FindPlayer

> Signature

     IPlayer FindPlayer(string partialNameOrIdOrIp, PlayerFilter playerFilter)

> Implementation

     void Loaded(IPlayerManager playerManager)
    {
        IPlayer player = playerManager.FindPlayer("Calytic");
        if (player.IsConnected)
        {
            Logger.Info("Calytic is online!");
        }
    }

### PlayerFilter

The player search may be filtered according the following bitwise enumerator or any combination of the following characteristics.

     enum PlayerFilter
    {
        All = 0,
        Connected = 1,
        Admin = 2,
        Moderator = 4,
        Banned = 8,
        Alive = 16,
        Dead = 32,
        Sleeping = 64
    }

Filters may be combined using a `bitwise or` or `pipe operator`, for example...

     IPlayer player = playerManager.FindPlayer("Calytic", PlayerFilter.Connected | PlayerFilter.Alive);
    if (player != null)
    {
        Logger.Info("Calytic is online AND alive");
    }

### FindPlayers

Find multiple players who match the specified partial name.

> Signature

    IEnumerable<IPlayer> FindPlayers(string partialNameOrIdOrIp, PlayerFilter playerFilter)

> Implementation

     void Loaded(IPlayerManager playerManager)
    {
        IEnumerable<IPlayer> players = playerManager.FindPlayers("Cal");
        Logger.Info($"Found {players.Count()} players");
        foreach(IPlayer player in players)
        {
            Logger.Info(player.Name);
        }
    }

Find multiple players who match the specified filter.

> Signature

    IEnumerable<IPlayer> FindPlayers(PlayerFilter playerFilter)

> Implementation

     void Loaded(IPlayerManager playerManager)
    {
        IEnumerable<IPlayer> players = playerManager.FindPlayers(PlayerFilter.Sleeping);
        Logger.Info($"Found {players.Count()} sleeping players");
        foreach(IPlayer player in players)
        {
            Logger.Info(player.Name);
        }
    }

### FindPlayerById

Find a specific player which may often be faster than the above methods.

> Signature

     IPlayer FindPlayerById(string playerId)

> Implementation

     IPlayer player = playerManager.FindPlayerById("playerid");

### FindPlayerByObj

Find an `IPlayer` from the game-specific player object.

> Signature

     IPlayer FindPlayerByObj(object playerObj)

> Implementation

     IPlayer player = playerManager.FindPlayerByObj(basePlayer);

### GamePlayer.IPlayer Property

As an alternative to the above `FindPlayerByObj`, the `IPlayer` instance may also be obtained from an `IPlayer` property patched into the game-specific player object.

     IPlayer player = basePlayer.IPlayer;

### IPlayer.Object property

Get a the GamePlayer associated with an IPlayer.

> Signature

     object Object;

> Implementation

     object player = player.Object;

## IPlayer and "GamePlayer" substitution

`IPlayer` is the universal representation of a player available across all games. `GamePlayer` itself _does not_ exist but refers to whatever concrete implementation that represents a player within a specific game (e.g. `BasePlayer`, `PlayerSession`, etc).

Hook and command parameters may substitute an `IPlayer` instance for a `GamePlayer` instance and vice-versa. Substitution is _not_ a casting operation as the parameters are substituted at run-time. This substitution is a special behavior and does not rely on the service container like normal parameter substitution.

     void OnPlayerConnected(IPlayer player) { }
    void OnPlayerConnected(BasePlayer player) { } // Rust
    void OnPlayerConnected(PlayerSession player) { } // Hurtworld
    // etc..

## OnlinePlayers decorator

Scaffold custom data structures to automatically wrap the `IPlayer` instances when new players connect.

     class PlayerDisease
    {
        public IPlayer Player;
        public bool HasPlague = false;
        public bool HasScurvy = false;
    }

    [OnlinePlayers]
    private Hash<IPlayer, PlayerDisease> DiseasedPlayers = new Hash<IPlayer, PlayerDisease>();

In the above example, when a player connects a new `PlayerDisease` object is created and added to the `DiseasedPlayers` hash table. Conversely, when the player disconnects they are removed from the hash table.

The hash table may be keyed by an `IPlayer` instance or a `GamePlayer` instance and the decorator must define a corresponding field named `Player` by the same type as the hash key. Any type of hash table may be used, including a `Dictionary<,>`.

### OnlinePlayers OnConnect/OnDisconnect

If the wrapper class needs to be notified when the player connects or disconnects, the `OnConnect` and/or `OnDisconnect` methods may be implemented.

     class PlayerDisease
    {
        public IPlayer Player;
        public bool HasPlague = false;
        public bool HasScurvy = false;

        void OnConnect()
        {
            // The player connected
        }

        void OnDisconnect()
        {
            // The player disconnected
        }
    }

### OnlinePlayers file persistence

Instead of implementing file handling to save and load wrapper classes, use the `OnlinePlayers` attribute path option.

    [OnlinePlayers("diseased_players/{Id}.json")]
    private Hash<IPlayer, PlayerDisease> DiseasedPlayers = new Hash<IPlayer, PlayerDisease>();

The PlayerDisease data will now be persisted automatically to/from the JSON file `umod/data/diseased_players/############.json` when the player connects or disconnects.

GatesIntegration

### The Basics

  1. Plugins
  2. Hooks
  3. Commands
  4. Validation
  5. Configuration
  6. Localization
  7. Schematics
  8. Filesystem
  9. Preprocessors
  10. Gates
  11. **Players**
  12. Integration
  13. Timers
  14. Logging
  15. Text Styling
