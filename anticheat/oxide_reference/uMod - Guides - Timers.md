# uMod - Guides - Timers

1 minute to read

Created by

Cal<PERSON>

Updated by

<PERSON><PERSON>

#  Timers

####  Details on how to use timers.

This guide is for uMod,_**not** Oxide_.

  * Once
  * Every
  * Repeat
  * RepeatUntil
  * NextFrame
  * Destroy timers

A timer invokes a callback or anonymous function after a set interval.

## Once

Execute once after the specified delay interval (in seconds).

    timer.Once(1f, () =>
    {
        Logger.Info("Hello world!");
    });

## Every

Execute forever or until the timer is manually destroyed (or the plugin is unloaded).

    timer.Every(3f, () =>
    {
        Logger.Info("Hello world!");
    });

## Repeat

Execute repeatedly until the timer is executed the specified number of times.

    timer.Repeat(5f, 0, () =>
    {
        Logger.Info("Hello world!");
    });

## RepeatUntil

Execute repeatedly until the specified condition is satisfied.

     int count = 0;
    timer.RepeatUntil(5f, () => count == 5, () =>
    {
        count++;
        Logger.Info($"Hello {count}");
    });

## NextFrame

Execute immediately in the next frame.

    timer.NextFrame(() =>
    {
        Logger.Info("Hello world!");
    });

## Destroy timers

When a timer is no longer operating, it is marked as destroyed. Timers may also be destroyed manually if stored in a variable.

     Timer myTimer = timer.Every(3f, () =>
    {
        Logger.Info("Hello world!");
    });

    myTimer.Destroy();
    if (myTimer.Destroyed)
    {
        Logger.Info("Timer destroyed!");
    }

IntegrationLogging

### The Basics

  1. Plugins
  2. Hooks
  3. Commands
  4. Validation
  5. Configuration
  6. Localization
  7. Schematics
  8. Filesystem
  9. Preprocessors
  10. Gates
  11. Players
  12. Integration
  13. **Timers**
  14. Logging
  15. Text Styling
