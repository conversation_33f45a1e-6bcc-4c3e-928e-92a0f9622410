# uMod - Preprocessor Directives

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Basic usage
  * Available symbols
  * Custom symbols
    * Example

# Preprocessor Directives

When writing or debugging plugins, it is useful to be familiar with preprocessor directives.

* * *

## Basic usage

Each game and branch that Oxide supports has an accompanying preprocessor symbol.

In universal plugins, this symbol may be used to separate code for different games. This is used frequently to ensure that blocks of code are only compiled for a particular game.

     private bool CanUserLogin(string name, string id, string ip)
    {
    #if RUST
        // Do Rust-specific code
    #elif HURTWORLD
        // Do Hurtworld-specific code
    #endif
    }

When a plugin is compiled for Rust, only the first code will be compiled. Conversely, in a Hurtworld context, only the second code will be used.

## Available symbols

Rust|


    RUST

---|---
Hurtworld|


    HURTWORLD

7 Days To Die|


    SEVENDAYS

7 Days To Die latest_experimental|


    SEVENDAYS
    SEVENDAYSLATEST_EXPERIMENTAL

Reign Of Kings|


    REIGNOFKINGS

The Forest|


    THEFOREST

## Custom symbols

In some plugins you may see the `DEBUG` symbol or other symbols used. These are primarily for testing purposes.

### Example

     #if DEBUG
        Puts("Some test related info");
    #endif

In order to enable these debugging code blocks, a preprocessor directive must be defined at the beginning of the plugin file.

     #define DEBUG

    using System;
