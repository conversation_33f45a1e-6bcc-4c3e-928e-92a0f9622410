# uMod - Dependencies

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Optional dependencies
    * Basic plugin reference
  * Required dependencies
  * Hard dependencies

# Dependencies

Dependencies are functional relationships between different code resources.

* * *

## Optional dependencies

Optional dependencies are plugins, extensions, products, or third-party libraries that your code _can_ use but does not require to work.

Generally an optional dependency means checking for the installation of another resource (i.e. plugin), and disabling or enabling features depending on its existence.

### Basic plugin reference

A plugin reference field allows developers to magically reference another plugin. If the plugin is not available, the reference field will return `null`.

The name of the plugin reference field must match the class name of the plugin being referenced.

    [PluginReference]
    private Plugin EpicStuff;

    private void Loaded()
    {
        if (EpicStuff != null)
        {
            EpicStuff.Call("SomeMethod");
        }
    }

## Required dependencies

Resource requirements are plugins, extensions, products, or third-party libraries that a plugin _must_ have in order to work.

Requirements are _not_ optional, and the code must check for them and display a helpful error message when they are unavailable.

     private void Loaded()
    {
        if (EpicStuff == null)
        {
            LogError("Epic Stuff is not loaded, get it at https://umod.org");
        }
    }

## Hard dependencies

"Hard" dependencies are special cases where a resource (i.e. plugin) is compiled alongside another plugin and may be referenced directly (as opposed to indirectly through Oxide).

It is _not_ recommended to use hard dependencies except when it is absolutely needed.

###### If a hard dependency is not installed alongside a plugin that requires it, a compilation error will be thrown.

At the top of the plugin file, above using statements:

     // Requires: EpicStuff

In the plugin body:

     // EpicStuff may now be referenced directly
    EpicStuff EpicStuff;

    private void Loaded()
    {
        EpicStuff = (EpicStuff)Manager.GetPlugin("EpicStuff");
    }
