# uMod - Configuration

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Simple configuration
    * Creating
    * Updating
  * Advanced configuration
    * Updating
    * Loading

# Configuration

Since many users can not or do not want to edit the plugin directly to change settings or options, it is best to offer them a configuration file that can easily be edited without worrying about messing up the plugin or it resetting each time the plugin is updated.

* * *

## Simple configuration

### Creating

There are many methods to create a configuration file. The examples below outline the simplest possible usage.

     protected override void LoadDefaultConfig()
    {
        LogWarning("Creating a new configuration file");
        Config["ShowJoinMessage"] = true;
        Config["ShowLeaveMessage"] = true;
        Config["JoinMessage"] = "Welcome to this server";
        Config["LeaveMessage"] = "Goodbye";
    }

### Updating

Modify and save configuration entries by simply assigning the new values and calling the save function.

    [Command("test")]
    private void TestCommand(IPlayer player, string command, string[] args)
    {
        Config["ShowJoinMessage"] = !(bool)Config["ShowJoinMessage"];
        SaveConfig();
    }

## Advanced configuration

For large plugins with more elaborate configurations, it may be helpful to scaffold a formal configuration class.

     private class PluginConfig
    {
        public bool ShowJoinMessage;
        public bool ShowLeaveMessage;
        public string JoinMessage;
        public string LeaveMessage;
    }

### Updating

Write the configuration object directly to a file using this simple one-liner.

     private void SaveConfig()
    {
        Config.WriteObject(config, true);
    }

### Loading

Load the configuration object directly from a file.

     private PluginConfig config;

    private void Init()
    {
        config = Config.ReadObject<PluginConfig>();
    }

    protected override void LoadDefaultConfig()
    {
        Config.WriteObject(GetDefaultConfig(), true);
    }

    private PluginConfig GetDefaultConfig()
    {
        return new PluginConfig
        {
            ShowJoinMessage = true,
            ShowLeaveMessage = true,
            JoinMessage = "Welcome to this server",
            LeaveMessage = "Goodbye"
        };
    }
