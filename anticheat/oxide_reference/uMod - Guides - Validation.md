# uMod - Guides - Validation

8 minutes to read

Created by

Cal<PERSON>

Updated by

<PERSON><PERSON>

#  Validation

####  Basic input validator usage

This guide is for uMod,_**not** Oxide_.

  * Introduction
  * Command validation
    * IsValid
    * HasArgument
    * IsDefault
    * IsArgument
    * GetArgument
    * Argument Index
  * Helper methods
    * GetString
    * GetBool / GetBoolean
    * GetFloat
    * GetDouble
    * GetDecimal
    * GetShort
    * GetUShort
    * GetByte
    * GetSByte
    * GetInt / GetInteger
    * GetUInt
    * GetLong / GetInt64
    * GetULong / GetUInt64
    * GetTimeSpan
    * GetDateTime
    * GetPoint
    * GetPosition
    * GetPosition4

## Introduction

Command input must be validated to ensure that the user is properly notified of invalid input and the command operates as expected.

## Command validation

All of the following methods apply to commands that have command definitions where named arguments are specified.

### IsValid

When a command specifies required arguments in the command definition, it is trivial to determine if all of the required arguments were provided with the `IArgs.IsValid` property.

    [Command("poke {player}")]
    void PokeCommand(IPlayer player, IArgs args)
    {
        if (args.IsValid)
        {
            // Execute the command
        }
        else
        {
            // Invalid syntax
        }
    }

### HasArgument

The existence of an optional argument may be determined with the `IArgs.HasArgument` method.

    [Command("poke {player?}")]
    void PokeCommand(IPlayer player, IArgs args)
    {
        if (args.HasArgument("player"))
        {
            // Do something with the argument
        }
        else
        {
            // Invalid syntax
        }
    }

### IsDefault

When a named argument specifies a default value, the `IArgs.IsDefault` method can determine whether the default value was provided.

    [Command("poke {player=Calytic}")]
    void PokeCommand(IPlayer player, IArgs args)
    {
        if (args.IsDefault("player"))
        {
            // Do something with the default argument
        }
    }

### IsArgument

The `IArgs.IsArgument` method attempts to parse the provided string as the type specified and determine if it is valid as that type.

    [Command("poke {player}")]
    void PokeCommand(IPlayer player, IArgs args)
    {
        if (args.IsArgument<ulong>("player"))
        {
            ulong playerID = args.GetUInt64("player");
            // Do something with the ulong
        }
    }

### GetArgument

The `GetArgument` method provides a simple way to get a named argument and may specify an optional default if the argument is not supplied.

    [Command("poke {player?}")]
    void PokeCommand(IPlayer player, IArgs args)
    {
        string playerArg = args.GetArgument("player", "Calytic");
        // Do something with the argument
    }

The `TryGetArgument` method will pipe the argument to an `out` parameter and return `true` or `false` if the argument exists.

    [Command("poke {player}")]
    void PokeCommand(IPlayer player, IArgs args)
    {
        if (args.TryGetArgument("player", out string playerArg))
        {
            // Do something with the argument
        }
        else
        {
            // No argument found
        }
    }

### Argument Index

Arguments can be accessed directly by name or by index. Accessing arguments by index is not recommended because argument order may change. It exists mainly for compatibility with legacy command implementations.

For backwards compatibility, accessing an argument by index will attempt to convert the argument to a string.

    [Command("poke {player}")]
    void PokeCommand(IPlayer player, IArgs args)
    {
        string playerArg = args[0];

        if(string.IsNullOrEmpty(playerArg))
        {
            // Do something with the argument
        }
        else
        {
            // No argument found
        }
    }

Accessing an argument by name will return an `object` or `null`.

    [Command("poke {player}")]
    void PokeCommand(IPlayer player, IArgs args)
    {
        object playerArg = args["player"];

        if(playerArg != null)
        {
            // Do something with the argument
        }
        else
        {
            // No argument found
        }
    }

## Helper methods

Similar to `GetArgument` a series of methods are provided which implicitly handle type casting and may perform better.

### GetString

Gets the default string argument.

> Signature

     string GetString(string name, string @default = default)

> Implementation

     string argument = args.GetString("player", "default");

### GetBool / GetBoolean

Gets the boolean representation of an argument.

> Signature

     bool GetBool(string name, bool @default = default)

> Implementation

     bool argument = args.GetBool("switch", true);

Note that the following values are considered valid boolean values.

> True

     "1"
    "true"
    "yes"
    "y"

> False

     "0"
    "false"
    "no"
    "n"
    "none"
    "null"

### GetFloat

Gets the float representation of the argument.

> Signature

     float GetFloat(string name, float @default = 0f)

> Implementation

     float argument = args.GetFloat("myFloat", 3.14f);

### GetDouble

Gets the double representation of the argument.

> Signature

     double GetDouble(string name, double @default = 0)

> Implementation

     double argument = args.GetDouble("myDouble", 3.14);

### GetDecimal

Gets the decimal representation of the argument.

> Signature

     decimal GetDecimal(string name, decimal @default = 0)

> Implementation

     decimal argument = args.GetDecimal("myDecimal", 3.14M);

### GetShort

Gets the short (-32768, 32767) representation of the argument.

> Signature

     short GetShort(string name, short @default = 0)

> Implementation

     short argument = args.GetShort("myShort", 3);

### GetUShort

Gets the unsigned short (0, 65535) representation of the argument.

> Signature

     ushort GetUShort(string name, ushort @default = 0)

> Implementation

     ushort argument = args.GetUShort("myUShort", 3);

### GetByte

Gets the byte (0, 255) representation of the argument.

> Signature

     byte GetByte(string name, byte @default = 0)

> Implementation

     byte argument = args.GetByte("myByte", 3);

### GetSByte

Gets the signed byte (-128, 127) representation of the argument.

> Signature

     sbyte GetSByte(string name, sbyte @default = 0)

> Implementation

     sbyte argument = args.GetSByte("mySByte", 3);

### GetInt / GetInteger

Gets the integer (-2,147,483,648, 2,147,483,647) representation of the argument.

> Signature

     int GetInteger(string name, int @default = 0)

> Implementation

     int argument = args.GetInteger("myInteger", 3);

### GetUInt

Gets the unsigned integer (0, 4,294,967,295) representation of the argument.

> Signature

     uint GetUInt(string name, uint @default = 0)

> Implementation

     uint argument = args.GetUInt("myUInteger", 3);

### GetLong / GetInt64

Gets the long (~-9 quintillion, ~9 quintillion) representation of the argument.

> Signature

     long GetInt64(string name, long @default = 0)

> Implementation

     long argument = args.GetInt64("myLong", 3);

### GetULong / GetUInt64

Gets the ulong (0, ~18 quintillion) representation of the argument.

> Signature

     ulong GetUInt64(string name, ulong @default = 0)

> Implementation

     ulong argument = args.GetUInt64("myULong", 3);

### GetTimeSpan

Gets the time span representation of the argument.

> Signature

     TimeSpan GetTimeSpan(string name)

> Implementation

     TimeSpan timeSpan = args.GetTimeSpan("myTimeSpan");

### GetDateTime

Gets the date-time representation of the argument.

> Signature

     DateTime GetDateTime(string name)

> Implementation

     DateTime dateTime = args.GetDateTime("myDateTime");

### GetPoint

Gets the point representation of the argument.

> Signature

     Point GetPoint(string name, Point @default = null)

> Implementation

     Point position = args.GetPoint("myPosition", Point.Empty);

### GetPosition

Gets the position representation of the argument.

> Signature

     Position GetPosition(string name, Position @default = null)

> Implementation

     Position position = args.GetPosition("myPosition", Position.Empty);

### GetPosition4

Gets the position (4d) representation of the argument.

> Signature

     Position4 GetPosition4(string name, Position4 @default = null)

> Implementation

     Position4 position4 = args.GetPosition4("myPosition", Position4.Empty);

CommandsConfiguration

### The Basics

  1. Plugins
  2. Hooks
  3. Commands
  4. **Validation**
  5. Configuration
  6. Localization
  7. Schematics
  8. Filesystem
  9. Preprocessors
  10. Gates
  11. Players
  12. Integration
  13. Timers
  14. Logging
  15. Text Styling
