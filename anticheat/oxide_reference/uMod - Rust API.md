# uMod - Rust API

#### Rust

Server

  * Init
  * OnServerRestartInterrupt
  * OnServerShutdown
  * OnServerCommand
  * OnMessagePlayer
  * OnFrame
  * OnServerInformationUpdated
  * OnRconCommand
  * OnRconConnection
  * OnPluginLoaded
  * OnNewSave
  * OnSaveLoad
  * OnPluginUnloaded
  * OnServerMessage
  * OnServerInitialized
  * OnTick
  * OnServerSave

Player

  * CanAffordUpgrade
  * CanAssignBed
  * CanUpdateSign
  * OnUserChat
  * OnPlayerCommand
  * OnUserCommand
  * OnPlayerRevive
  * CanLock
  * OnMeleeAttack
  * OnPlayerRecovered
  * CanPushBoat
  * CanDeployItem
  * OnPlayerAssist
  * OnPlayerSetInfo
  * CanSpectateTarget
  * OnActiveItemChange
  * OnActiveItemChanged
  * OnPayForUpgrade
  * OnMapMarkerRemove
  * OnMapMarkerAdd
  * OnMapMarkerAdded
  * OnMapMarkersClear
  * OnMapMarkersCleared
  * OnPayForPlacement
  * CanAffordToPlace
  * OnPlayerKeepAlive
  * OnSendCommand
  * OnBroadcastCommand
  * OnUserRespawn
  * OnUserRespawned
  * OnPlayerReported
  * OnPlayerCorpse
  * CanUseWires
  * OnPlayerCorpseSpawn
  * OnPlayerCorpseSpawned
  * OnUserConnected
  * OnUserDisconnected
  * CanBeTargeted
  * CanBeWounded
  * CanBuild
  * CanBypassQueue
  * CanChangeCode
  * CanChangeGrade
  * CanCraft
  * CanClientLogin
  * CanUserLogin
  * OnUserApproved
  * CanDemolish
  * CanHackCrate
  * OnUserNameUpdated
  * CanDismountEntity
  * OnUserGroupAdded
  * OnUserGroupRemoved
  * CanEquipItem
  * OnExperimentStart
  * OnUserPermissionGranted
  * OnUserPermissionRevoked
  * OnExperimentStarted
  * OnUserKicked
  * OnExperimentEnd
  * OnExperimentEnded
  * OnUserBanned
  * CanHideStash
  * OnUserUnbanned
  * CanLootPlayer
  * CanLootEntity
  * CanMountEntity
  * CanPickupEntity
  * CanPickupLock
  * CanRenameBed
  * CanResearchItem
  * CanUseLockedEntity
  * CanSeeStash
  * OnStashExposed
  * OnStashHidden
  * CanSetBedPublic
  * CanUnlock
  * CanUseMailbox
  * CanUseUI
  * CanWearItem
  * OnClientAuth
  * OnDestroyUI
  * OnFindSpawnPoint
  * OnLootEntity
  * OnLootEntityEnd
  * OnLootItem
  * OnLootPlayer
  * OnPlayerAttack
  * OnPlayerBanned
  * OnPlayerChat
  * OnPlayerConnected
  * OnPlayerDeath
  * OnPlayerDisconnected
  * OnPlayerDropActiveItem
  * OnPlayerHealthChange
  * OnPlayerInput
  * OnPlayerKicked
  * OnPlayerLand
  * OnPlayerLanded
  * OnPlayerLootEnd
  * OnPlayerMetabolize
  * OnPlayerRecover
  * OnPlayerRespawn
  * OnPlayerRespawned
  * OnRespawnInformationGiven
  * OnPlayerSleep
  * OnPlayerSleepEnded
  * OnPlayerSpawn
  * OnPlayerSpectate
  * OnPlayerSpectateEnd
  * OnPlayerTick
  * OnPlayerViolation
  * OnPlayerVoice
  * OnPlayerWound
  * OnRunPlayerMetabolism
  * OnUserApprove
  * OnDefaultItemsReceive
  * OnDefaultItemsReceived
  * OnAnalysisComplete
  * OnNpcConversationStart
  * OnNpcConversationRespond
  * OnNpcConversationResponded
  * OnNpcConversationEnded
  * OnNetworkGroupEntered
  * OnNetworkGroupLeft
  * OnDemoRecordingStart
  * OnDemoRecordingStarted
  * OnDemoRecordingStop
  * OnDemoRecordingStopped
  * OnLootNetworkUpdate
  * OnInventoryNetworkUpdate
  * OnPlayerAddModifiers
  * OnThreatLevelUpdate
  * CanUseGesture
  * OnCentralizedBanCheck
  * OnClientCommand
  * OnCorpsePopulate
  * CanSetRelationship

Entity

  * CanBradleyApcTarget
  * OnNpcPlayerResume
  * OnNpcDestinationSet
  * OnNpcStopMoving
  * OnEntityMarkHostile
  * CanEntityBeHostile
  * CanSamSiteShoot
  * OnDieselEngineToggle
  * OnDieselEngineToggled
  * OnExcavatorMiningToggled
  * OnExcavatorGather
  * OnExcavatorResourceSet
  * OnInputUpdate
  * OnOutputUpdate
  * OnButtonPress
  * OnShopAcceptClick
  * OnShopCancelClick
  * OnShopCompleteTrade
  * OnSamSiteTarget
  * OnHelicopterStrafeEnter
  * OnSupplyDropLanded
  * OnEntityStabilityCheck
  * OnBuildingPrivilege
  * OnHorseLead
  * OnHorseHitch
  * OnWireConnect
  * OnWireClear
  * OnReactiveTargetReset
  * OnMlrsFire
  * OnMlrsFired
  * OnTurretAssign
  * OnTurretAssigned
  * CanHelicopterDropCrate
  * OnEngineLoadoutRefresh
  * CanHelicopterStrafe
  * CanHelicopterStrafeTarget
  * CanHelicopterTarget
  * CanHelicopterUseNapalm
  * CanNetworkTo
  * OnHelicopterRetire
  * CanNpcAttack
  * CanNpcEat
  * CanRecycle
  * OnAirdrop
  * OnBradleyApcInitialize
  * OnBradleyApcHunt
  * OnBradleyApcPatrol
  * OnContainerDropItems
  * OnCrateDropped
  * OnCrateHack
  * OnCrateHackEnd
  * OnCrateLanded
  * OnEntityDeath
  * OnEntityDismounted
  * OnEntityEnter
  * OnEntityGroundMissing
  * OnEntityKill
  * OnEntityLeave
  * OnEntityMounted
  * OnEntitySpawned
  * OnEntityTakeDamage
  * OnFireBallDamage
  * OnFireBallSpread
  * OnFlameExplosion
  * OnHelicopterAttack
  * OnHelicopterDropCrate
  * OnHelicopterDropDoorOpen
  * OnHelicopterKilled
  * OnHelicopterOutOfCrates
  * OnHelicopterTarget
  * OnLiftUse
  * OnLootSpawn
  * OnNpcTargetSense
  * OnNpcTarget
  * OnOvenToggle
  * OnItemRecycle
  * OnOvenCook
  * OnOvenCooked
  * OnRecyclerToggle
  * OnResourceDepositCreated
  * OnShopCompleteTrade
  * OnTurretAuthorize
  * OnTurretClearList
  * OnTurretDeauthorize
  * OnTurretModeToggle
  * OnTurretShutdown
  * OnTurretStartup
  * OnTurretTarget
  * OnTurretToggle
  * OnBigWheelWin
  * OnBigWheelLoss
  * OnSolarPanelSunUpdate
  * OnBookmarkControl
  * OnBookmarkControlStarted
  * OnBookmarkDelete
  * OnBookmarkAdd
  * OnBookmarksSendControl
  * OnBookmarkControlEnd
  * OnBookmarkControlEnded
  * OnRfBroadcasterAdd
  * OnRfBroadcasterAdded
  * OnRfBroadcasterRemove
  * OnRfBroadcasterRemoved
  * OnRfListenerAdd
  * OnRfListenerAdded
  * OnRfListenerRemove
  * OnRfListenerRemoved
  * OnSleepingBagDestroy
  * OnRfFrequencyChange
  * OnRfFrequencyChanged
  * OnSleepingBagDestroyed
  * OnNetworkSubscriptionsUpdate
  * OnBookmarkInput
  * OnEntityControl
  * OnTurretRotate
  * OnCounterTargetChange
  * OnCounterModeToggle
  * OnSwitchToggle
  * OnSwitchToggled
  * OnEntityDestroy
  * OnElevatorButtonPress
  * OnElevatorCall
  * OnElevatorMove
  * CanSwapToSeat
  * OnRidableAnimalClaim
  * OnRidableAnimalClaimed
  * OnEntitySaved
  * OnEntitySnapshot
  * OnIORefCleared
  * OnEntityFlagsNetworkUpdate
  * OnSupplyDropDropped
  * OnCargoPlaneSignaled
  * OnWaterPurify
  * OnWaterPurified
  * OnSleepingBagValidCheck
  * OnCCTVDirectionChange
  * OnWaterCollect
  * OnLiquidVesselFill
  * OnLockerSwap
  * CanLockerAcceptItem

Item

  * CanAcceptItem
  * OnBackpackDrop
  * OnCardSwipe
  * OnItemRemove
  * OnMapImageUpdated
  * CanDropActiveItem
  * CanCombineDroppedItem
  * CanMoveItem
  * CanStackItem
  * OnFuelConsumed
  * OnFuelConsume
  * OnFindBurnable
  * OnHealingItemUse
  * OnItemAction
  * OnItemAddedToContainer
  * OnItemCraft
  * OnItemCraftCancelled
  * OnItemCraftFinished
  * OnItemDeployed
  * OnItemDropped
  * OnItemPickup
  * OnItemRemovedFromContainer
  * OnItemRepair
  * OnItemResearch
  * OnItemResearched
  * OnResearchCostDetermine
  * OnItemSplit
  * OnItemUpgrade
  * OnItemUse
  * OnLoseCondition
  * OnMaxStackable
  * OnTrapArm
  * OnTrapDisarm
  * OnTrapSnapped
  * OnTrapTrigger
  * OnBonusItemDrop
  * OnBonusItemDropped
  * OnItemRefill
  * OnItemLock
  * OnItemUnlock
  * OnItemSubmit
  * OnItemStacked
  * OnIngredientsCollect

Resource

  * OnCollectiblePickup
  * CanTakeCutting
  * OnQuarryToggled
  * OnGrowableGathered
  * OnRemoveDying
  * OnGrowableGather
  * OnDispenserBonus
  * OnDispenserGather
  * OnQuarryEnabled
  * OnTreeMarkerHit
  * OnQuarryGather
  * OnSurveyGather

Structure

  * OnCodeEntered
  * OnCupboardAuthorize
  * OnCupboardClearList
  * OnCupboardDeauthorize
  * OnDoorClosed
  * OnDoorOpened
  * OnDoorKnocked
  * OnEntityBuilt
  * OnHammerHit
  * OnStructureDemolish
  * OnStructureRepair
  * OnStructureRotate
  * OnStructureUpgrade
  * OnConstructionPlace
  * OnBuildingSplit

Terrain

  * OnTerrainInitialized

Vending

  * CanAdministerVending
  * OnTakeCurrencyItem
  * OnGiveSoldItem
  * OnVendingShopRename
  * CanUseVending
  * CanVendingAcceptItem
  * OnAddVendingOffer
  * OnBuyVendingItem
  * OnDeleteVendingOffer
  * OnOpenVendingAdmin
  * OnVendingShopOpened
  * OnRefreshVendingStock
  * OnRotateVendingMachine
  * OnToggleVendingBroadcast
  * OnVendingTransaction
  * OnNpcGiveSoldItem

Weapon

  * CanCreateWorldProjectile
  * OnProjectileRicochet
  * OnExplosiveDud
  * OnAmmoUnload
  * OnExplosiveFuseSet
  * CanExplosiveStick
  * OnWorldProjectileCreate
  * OnExplosiveDropped
  * OnExplosiveThrown
  * OnFlameThrowerBurn
  * OnMeleeThrown
  * OnMagazineReload
  * OnWeaponReload
  * OnRocketLaunched
  * OnAmmoSwitch
  * OnWeaponFired

Vehicle

  * CanUseHelicopter
  * OnBoatPathGenerate
  * OnVehicleModuleMove
  * OnEngineStart
  * OnEngineStarted
  * OnEngineStopped
  * OnEngineStop
  * OnEngineStatsRefresh
  * OnEngineStatsRefreshed
  * OnVehicleModuleSelect
  * OnVehicleModuleSelected
  * OnVehicleModuleDeselected
  * OnHotAirBalloonToggle
  * OnHotAirBalloonToggled
  * OnVehicleModulesAssign
  * OnVehicleModulesAssigned
  * OnVehiclePush
  * CanCheckFuel
  * CanUseFuel
  * OnFuelCheck
  * OnFuelAmountCheck
  * OnFuelItemCheck

Team

  * OnTeamCreate
  * OnTeamInvite
  * OnTeamRejectInvite
  * OnTeamPromote
  * OnTeamLeave
  * OnTeamKick
  * OnTeamAcceptInvite
  * OnTeamDisband
  * OnTeamDisbanded
  * OnTeamUpdate
  * OnTeamUpdated
  * OnTeamCreated

World

  * OnWorldPrefabSpawned

Fishing

  * OnFishCaught
  * CanCatchFish
  * OnFishCatch
  * OnFishingStopped

Electronic

  * OnExcavatorSuppliesRequest
  * OnExcavatorSuppliesRequested

Clan

  * OnClanLogoChanged
  * OnClanColorChanged
  * OnClanMemberKicked
  * OnClanMemberLeft
  * OnClanMemberAdded
  * OnClanCreated
  * OnClanDisbanded

Plugin

  * Loaded
  * Unload
  * LoadDefaultConfig
  * LoadDefaultMessages

Sign

  * OnSignLocked
  * OnSignUpdated
  * OnSpinWheel

TechTree

  * CanUnlockTechTreeNode
  * CanUnlockTechTreeNodePath
  * OnTechTreeNodeUnlock
  * OnTechTreeNodeUnlocked

Phone

  * OnPhoneDial
  * CanReceiveCall
  * OnPhoneAnswer
  * OnPhoneAnswered
  * OnPhoneCallStart
  * OnPhoneCallStarted
  * OnPhoneDialFail
  * OnPhoneDialFailed
  * OnPhoneDialTimeout
  * OnPhoneDialTimedOut
  * OnPhoneNameUpdate
  * OnPhoneNameUpdated

Permission

  * OnPermissionRegistered
  * OnGroupPermissionGranted
  * OnGroupPermissionRevoked
  * OnGroupCreated
  * OnGroupDeleted
  * OnGroupTitleSet
  * OnGroupRankSet
  * OnGroupParentSet

Definitions

  * Item List
  * Skin List

# Rust Hooks

##  __Server Hooks

#####  __**Init**

  * Called when a plugin is being initialized
  * Other plugins may or may not be present, dependant on load order
  * No return behavior

     void Init()
    {
        Puts("Init works!");
    }

##### __**OnServerRestartInterrupt**

  * Called when a server restart is being cancelled
  * Returning a non-null value overrides default behavior

     object OnServerRestartInterrupt()
    {
        Puts("OnServerRestartInterrupt works!");
        return null;
    }

##### __**OnServerShutdown**

  * Useful for saving something / etc on server shutdown
  * No return behavior

     void OnServerShutdown()
    {
        Puts("OnServerShutdown works!");
    }

##### __**OnServerCommand**

  * Useful for intercepting commands before they get to their intended target
  * Returning a non-null value overrides default behavior

     object OnServerCommand(ConsoleSystem.Arg arg)
    {
        Puts("OnServerCommand works!");
        return null;
    }

##### __**OnMessagePlayer**

  * Useful for intercepting server messages before they get to their intended target
  * Returning a non-null value overrides default behavior

     object OnMessagePlayer(string message, BasePlayer player)
    {
        Puts("OnMessagePlayer works!");
        return null;
    }

##### __**OnFrame**

  * Called each frame
  * No return behavior

     void OnFrame()
    {
        Puts("OnFrame works!");
    }

##### __**OnServerInformationUpdated**

  * Called after all steam information for the server has has been updated
  * No return behavior

     void OnServerInformationUpdated()
    {
        Puts("OnServerInformationUpdated works!");
    }

##### __**OnRconCommand**

  * Called when an RCON command is run
  * No return behavior

     void OnRconCommand(IPAddress ip, string command, string[] args)
    {
        Puts("OnRconCommand works!");
    }

##### __**OnRconConnection**

  * Called when a new RCON connection is opened
  * Returning a non-null value overrides default behavior

     object OnRconConnection(IPAddress ip)
    {
        Puts("OnRconConnection works!");
        return null;
    }

##### __**OnPluginLoaded**

  * Called when any plugin has been loaded
  * No return behavior
  * Not to be confused with Loaded

     void OnPluginLoaded(Plugin plugin)
    {
        Puts($"Plugin '{plugin.Name}' has been loaded");
    }

##### __**OnNewSave**

  * Called when a new savefile is created (usually when map has wiped)
  * No return behavior

     void OnNewSave(string filename)
    {
      Puts("OnNewSave works!");
    }

##### __**OnSaveLoad**

  * Called when a save file is loaded
  * Returning a non-null value overrides default behavior

     object OnSaveLoad(Dictionary<BaseEntity, ProtoBuf.Entity> entities)
    {
        Puts("OnSaveLoad works!");
        return null;
    }

##### __**OnPluginUnloaded**

  * Called when any plugin has been unloaded
  * No return behavior
  * Not to be confused with Unload

     void OnPluginUnloaded(Plugin plugin)
    {
        Puts($"Plugin '{plugin.Name}' has been unloaded");
    }

##### __**OnServerMessage**

  * Called before a SERVER message is sent to a player
  * Return a non-null value to stop message from being sent

     //  Example that stops message from being sent

    object OnServerMessage(string message, string playerName, string color, ulong playerId)
    {
        if (message.Contains("gave"))
        {
            Puts($"Message to {playerName} ({playerId}) cancelled");
            return false;
        }

        return null;
    }

     // Example that does not stop message from being sent

    void OnServerMessage(string message, string playerName, string color, ulong playerId)
    {
        Puts($"{playerName} ({playerId}) was sent message: {message}");
    }

##### __**OnServerInitialized**

  * Called after the server startup has been completed and is awaiting connections
  * Also called for plugins that are hotloaded while the server is already started running
  * Boolean parameter, false if called on plugin hotload and true if called on server initialization
  * No return behavior

     void OnServerInitialized(bool initial)
    {
        Puts("OnServerInitialized works!");
    }

##### __**OnTick**

  * Called every tick (defined by the tick rate of the server)
  * For better performance, avoid using heavy calculations in this hook.
  * No return behavior

     void OnTick()
    {
        Puts("OnTick works!");
    }

##### __**OnServerSave**

  * Called before the server saves
  * No return behavior

     void OnServerSave()
    {
        Puts("OnServerSave works!");
    }

## __Player Hooks

#####  __**CanAffordUpgrade**

  * Called when the resources for an upgrade are checked
  * Returning true or false overrides default behavior

     bool CanAffordUpgrade(BasePlayer player, BuildingBlock block, BuildingGrade.Enum grade)
    {
        Puts("CanAffordUpgrade works!");
        return true;
    }

##### __**CanAssignBed**

  * Called when a player attempts to assign a bed or sleeping bag to another player
  * Returning a non-null value overrides default behavior

     object CanAssignBed(BasePlayer player, SleepingBag bag, ulong targetPlayerId)
    {
        Puts("CanAssignBed works!");
        return null;
    }

##### __**CanUpdateSign**

  * Called when the player attempts to change the text on a sign or lock it, or update a photo frame
  * Returning true or false overrides default behavior

     bool CanUpdateSign(BasePlayer player, Signage sign)
    {
        Puts("CanUpdateSign works!");
        return true;
    }

     bool CanUpdateSign(BasePlayer player, PhotoFrame photoFrame)
    {
        Puts("CanUpdateSign works!");
        return true;
    }

##### __**OnUserChat**

  * Called when a player sends a chat message to the server
  * Returning true overrides default behavior of chat, not commands

     object OnUserChat(IPlayer player, string message)
    {
        Puts($"{player.Name} said: {message}");
        return null;
    }

##### __**OnPlayerCommand**

  * Useful for intercepting players' commands before their handling
  * Returning a non-null value overrides default behavior

     object OnPlayerCommand(BasePlayer player, string command, string[] args)
    {
        Puts("OnPlayerCommand works!");
        return null;
    }

##### __**OnUserCommand**

  * Useful for intercepting players' commands before their handling
  * Returning a non-null value overrides default behavior

     object OnUserCommand(IPlayer player, string command, string[] args)
    {
        Puts("OnUserCommand works!");
        return null;
    }

##### __**OnPlayerRevive**

  * Called before the recover after reviving with a medical tool
  * Useful for canceling the reviving
  * Returning a non-null value cancels default behavior

     object OnPlayerRevive(BasePlayer reviver, BasePlayer player)
    {
        Puts($"{reviver.displayName} revived {player.displayName}");
        return null;
    }

##### __**CanLock**

  * Useful for canceling the lock action
  * Returning a non-null value cancels default behavior

     object CanLock(BasePlayer player, BaseLock baseLock)
    {
        Puts("CanLock works!");
        return null;
    }

##### __**OnMeleeAttack**

  * Useful for canceling melee attacks
  * Returning a non-null value cancels default behavior

     object OnMeleeAttack(BasePlayer player, HitInfo info)
    {
        Puts("OnMeleeAttack works!");
        return null;
    }

##### __**OnPlayerRecovered**

  * Called when the player was recovered
  * No return behavior

     void OnPlayerRecovered(BasePlayer player)
    {
        Puts("OnPlayerRecovered works!");
    }

##### __**CanPushBoat**

  * Useful for canceling boat push
  * Returning a non-null value cancels default behavior

     object CanPushBoat(BasePlayer player, MotorRowboat boat)
    {
        Puts("CanPushBoat works!");
        return null;
    }

##### __**CanDeployItem**

  * Useful for denying items' deployment
  * Returning a non-null value cancels default behavior

     object CanDeployItem(BasePlayer player, Deployer deployer, NetworkableId entityId)
    {
        Puts("CanDeployItem works!");
        return null;
    }

##### __**OnPlayerAssist**

  * Called when a player tries to assist target player (when target is wounded)
  * Returning a non-null value cancels default behavior

     object OnPlayerAssist(BasePlayer target, BasePlayer player)
    {
        Puts("OnPlayerAssist works!");
        return null;
    }

##### __**OnPlayerSetInfo**

  * Called when setting player's information _(aka console variables)_
  * No return behavior

     void OnPlayerSetInfo(Connection connection, string key, string value)
    {
        Puts($"{connection.userid}: {key} was set to {value}");
    }

##### __**CanSpectateTarget**

  * Called when spectate target is attempting to update
  * Returning a non-null value cancels default behavior

     object CanSpectateTarget(BasePlayer player, string filter)
    {
        Puts($"{player.displayName} tries to spectate with a filter: {filter}");
        return null;
    }

##### __**OnActiveItemChange**

  * Called when active item is attempting to update
  * Returning a non-null value cancels default behavior

     object OnActiveItemChange(BasePlayer player, Item oldItem, ItemId newItemId)
    {
        Puts("OnActiveItemChange works!");
        return null;
    }

##### __**OnActiveItemChanged**

  * Called when active item was changed
  * No return behavior

     void OnActiveItemChanged(BasePlayer player, Item oldItem, Item newItem)
    {
        Puts("OnActiveItemChanged works!");
    }

##### __**OnPayForUpgrade**

  * Called when player is paying for an upgrade. Useful for preventing paying for block upgrade
  * Returning a non-null value cancels default behavior

     object OnPayForUpgrade(BasePlayer player, BuildingBlock block, ConstructionGrade gradeTarget)
    {
        Puts("OnPayForUpgrade works!");
        return null;
    }

##### __**OnMapMarkerRemove**

  * Called when trying to remove a marker
  * Returning a non-null value cancels default behaviour

     object OnMapMarkerRemove(BasePlayer player, MapNote note)
    {
        Puts("OnMapMarkerRemove works!");
        return null;
    }

##### __**OnMapMarkerAdd**

  * Called when trying to add a marker
  * Returning a non-null value cancels default behavior

     object OnMapMarkerAdd(BasePlayer player, MapNote note)
    {
        Puts("OnMapMarkerAdd works!");
        return null;
    }

##### __**OnMapMarkerAdded**

  * Called after a marker was added
  * No return behavior

     void OnMapMarkerAdded(BasePlayer player, MapNote note)
    {
        Puts("OnMapMarkerAdded works!");
    }

##### __**OnMapMarkersClear**

  * Called when trying to clear map markers
  * Returning a non-null value cancels default behavior

     object OnMapMarkersClear(BasePlayer player, List<MapNote> notes)
    {
        Puts("OnMapMarkersClear works!");
        return null;
    }

##### __**OnMapMarkersCleared**

  * Called after markers were cleared
  * No return behavior

     void OnMapMarkersCleared(BasePlayer player, List<MapNote> notes)
    {
        Puts("OnMapMarkersCleared works!");
    }

##### __**OnPayForPlacement**

  * Called when a player is paying for placement. Useful for preventing paying for placing deployables, building blocks and etc
  * Returning a non-null value cancels default behavior

     object OnPayForPlacement(BasePlayer player, Planner planner, Construction construction)
    {
        Puts("OnPayForPlacement works!");
        return null;
    }

##### __**CanAffordToPlace**

  * Useful for ignoring resource requirements for placement
  * Returning true or false overrides default behavior

     bool CanAffordToPlace(BasePlayer player, Planner planner, Construction construction)
    {
        Puts("CanAffordToPlace works!");
        return false;
    }

##### __**OnPlayerKeepAlive**

  * Called before a player is kept alive (Example: You started "helping" player, it keeps him alive for at least 10 seconds more to be sure he won't die until you finish picking him up)
  * Returning a non-null value cancels default behavior

     object OnPlayerKeepAlive(BasePlayer player, BasePlayer target)
    {
        Puts("OnPlayerKeepAlive works!");
        return null;
    }

##### __**OnSendCommand**

  * Called before a command is sent from the server to a player (or a group of players)
  * Usually commands aren't sent to a group of players, so in most cases it's safe to use only OnSendCommand with a single Connection.
  * Returning a non-null value overwrites command arguments

     object OnSendCommand(List<Connection> connections, string command, object[] args)
    {
        Puts("OnSendCommand works!");
        return null;
    }

    object OnSendCommand(Connection connection, string command, object[] args)
    {
        Puts("OnSendCommand works!");
        return null;
    }

##### __**OnBroadcastCommand**

  * Called before a command is broadcasted to all connected clients
  * Returning a non-null value overwrites command arguments

     object OnBroadcastCommand(string command, object[] args)
    {
        Puts("OnBroadcastCommand works!");
        return null;
    }

##### __**OnUserRespawn**

  * Called when a player is respawning
  * No return behavior

     void OnUserRespawn(IPlayer player)
    {
        Puts("OnUserRespawn works!");
    }

##### __**OnUserRespawned**

  * Called after a player has respawned
  * No return behavior

     void OnUserRespawned(IPlayer player)
    {
        Puts("OnUserRespawned works!");
    }

##### __**OnPlayerReported**

  * Called when a player has reported someone via F7
  * No return behavior

     void OnPlayerReported(BasePlayer reporter, string targetName, string targetId, string subject, string message, string type)
    {
        Puts($"{reporter.displayName} reported {targetName} for {subject}.");
    }

##### __**OnPlayerCorpse**

  * Called when a non-null corpse has just been spawned
  * No return behavior

     void OnPlayerCorpse(BasePlayer player, BaseCorpse corpse)
    {
        Puts($"A corpse for {player.displayName} has just been spawned!");
    }

##### __**CanUseWires**

  * Useful for allowing or preventing a player from using wires
  * Returning a non-null value overrides default behavior

     object CanUseWires(BasePlayer player)
    {
        Puts($"{player.displayName} has just tried to use wires");
        return null;
    }

##### __**OnPlayerCorpseSpawn**

  * Called when a non-null corpse is about to spawn
  * Returning a non-null value overrides default behavior

     object OnPlayerCorpseSpawn(BasePlayer player)
    {
        Puts("OnPlayerCorpseSpawn works!");
        return null;
    }

##### __**OnPlayerCorpseSpawned**

  * Called when a non-null corpse has just been spawned
  * No return behavior

     void OnPlayerCorpseSpawned(BasePlayer player, PlayerCorpse corpse)
    {
        Puts("OnPlayerCorpseSpawned works!");
    }

##### __**OnUserConnected**

  * Called after a player has been approved and has connected to the server
  * No return behavior

     void OnUserConnected(IPlayer player)
    {
        Puts($"{player.Name} ({player.Id}) connected from {player.Address}");

        if (player.IsAdmin)
        {
            Puts($"{player.Name} ({player.Id}) is admin");
        }

        Puts($"{player.Name} is {(player.IsBanned ? "banned" : "not banned")}");

        server.Broadcast($"Welcome {player.Name} to {server.Name}!");
    }

##### __**OnUserDisconnected**

  * Called after a player has disconnected from the server
  * No return behavior

     void OnUserDisconnected(IPlayer player)
    {
        Puts($"{player.Name} ({player.Id}) disconnected");
    }

##### __**CanBeTargeted**

  * Called when an autoturret, flame turret, shotgun trap, or helicopter turret is attempting to target the player
  * Returning true or false overrides default behavior

     bool CanBeTargeted(BaseCombatEntity player, MonoBehaviour behaviour)
    {
        Puts("CanBeTargeted works!");
        return true;
    }

##### __**CanBeWounded**

  * Called when any damage is attempted on player
  * Returning true or false overrides default behavior

     bool CanBeWounded(BasePlayer player, HitInfo info)
    {
        Puts("CanBeWounded works!");
        return true;
    }

##### __**CanBuild**

  * Called when the player tries to build something
  * Returning a non-null value overrides default behavior

     object CanBuild(Planner planner, Construction prefab, Construction.Target target)
    {
        Puts("CanBuild works!");
        return null;
    }

##### __**CanBypassQueue**

  * Called before the player is added to the connection queue
  * Returning true will bypass the queue, returning nothing will by default queue the player

     bool CanBypassQueue(Network.Connection connection)
    {
        Puts("CanBypassQueue works!");
        return true;
    }

##### __**CanChangeCode**

  * Called when a player tries to change the code on a codelock
  * Returning a non-null value overrides default behavior

     object CanChangeCode(BasePlayer player, CodeLock codeLock, string newCode, bool isGuestCode)
    {
        Puts("CanChangeCode works!");
        return null;
    }

##### __**CanChangeGrade**

  * Called when a player tries to change a building grade
  * Returning true or false overrides default behavior

     bool CanChangeGrade(BasePlayer player, BuildingBlock block, BuildingGrade.Enum grade)
    {
        Puts("CanChangeGrade works!");
        return true;
    }

##### __**CanCraft**

  * Called when the player attempts to craft an item
  * Returning true or false overrides default behavior

     bool CanCraft(ItemCrafter itemCrafter, ItemBlueprint bp, int amount)
    {
        Puts("CanCraft works!");
        return true;
    }

     bool CanCraft(PlayerBlueprints playerBlueprints, ItemDefinition itemDefinition, int skinItemId)
    {
        Puts("CanCraft works!");
        return true;
    }

##### __**CanClientLogin**

  * Called when the player is attempting to login
  * Returning a string will use the string as the error message
  * Returning true allows the connection, returning nothing will by default allow the connection, returning anything else will reject it with an error message

     object CanClientLogin(Network.Connection connection)
    {
        Puts("CanClientLogin works!");
        return true;
    }

##### __**CanUserLogin**

  * Called when a player is attempting to connect to the server
  * Returning a string will kick the user with this reason. Returning a non-null value overrides default behavior

     object CanUserLogin(string name, string id, string ipAddress)
    {
        if (name.ToLower().Contains("admin"))
        {
            Puts($"{name} ({id}) at {ipAddress} tried to connect with 'admin' in name");
            return "Sorry, your name cannot have 'admin' in it";
        }

        return true;
    }

##### __**OnUserApproved**

  * Called after a player is approved to connect to the server
  * No return behavior

     void OnUserApproved(string name, string id, string ipAddress)
    {
        Puts($"{name} ({id}) at {ipAddress} has been approved to connect");
    }

##### __**CanDemolish**

  * Called when a player tries to demolish a building block
  * Returning true or false overrides default behavior

     bool CanDemolish(BasePlayer player, BuildingBlock block, BuildingGrade.Enum grade)
    {
        Puts("CanDemolish works!");
        return true;
    }

##### __**CanHackCrate**

  * Called when a player starts hacking a locked crate
  * Returning a non-null value overrides default behavior

     object CanHackCrate(BasePlayer player, HackableLockedCrate crate)
    {
        Puts("CanHackCrate works!");
        return null;
    }

##### __**OnUserNameUpdated**

  * Called when a player's stored nickname has been changed
  * No return behavior

     void OnUserNameUpdated(string id, string oldName, string newName)
    {
        Puts($"Player name changed from {oldName} to {newName} for ID {id}");
    }

##### __**CanDismountEntity**

  * Called when the player attempts to dismount an entity
  * Returning a non-null value overrides default behavior

     object CanDismountEntity(BasePlayer player, BaseMountable entity)
    {
        Puts("CanDismountEntity works!");
        return null;
    }

##### __**OnUserGroupAdded**

  * Called when a player has been added to a group
  * No return behavior

     void OnUserGroupAdded(string id, string groupName)
    {
        Puts($"Player '{id}' added to group: {groupName}");
    }

##### __**OnUserGroupRemoved**

  * Called when a player has been removed from a group
  * No return behavior

     void OnUserGroupRemoved(string id, string groupName)
    {
        Puts($"Player '{id}' removed from group: {groupName}");
    }

##### __**CanEquipItem**

  * Called when the player attempts to equip an item
  * Returning true or false overrides default behavior

     bool CanEquipItem(PlayerInventory inventory, Item item, int targetPos)
    {
        Puts("CanEquipItem works!");
        return true;
    }

##### __**OnExperimentStart**

  * Called when the player attempts to experiment with at a workbench
  * Returning a non-null value overrides default behavior

     object OnExperimentStart(Workbench workbench, BasePlayer player)
    {
        Puts("OnExperimentStart works!");
        return null;
    }

##### __**OnUserPermissionGranted**

  * Called when a permission has been granted to a player
  * No return behavior

     void OnUserPermissionGranted(string id, string permName)
    {
        Puts($"Player '{id}' granted permission: {permName}");
    }

##### __**OnUserPermissionRevoked**

  * Called when a permission has been revoked from a player
  * No return behavior

     void OnUserPermissionRevoked(string id, string permName)
    {
        Puts($"Player '{id}' revoked permission: {permName}");
    }

##### __**OnExperimentStarted**

  * Called after the experimentation has started
  * No return behaviour

     void OnExperimentStarted(Workbench workbench, BasePlayer player)
    {
        Puts("OnExperimentStarted works!");
    }

##### __**OnUserKicked**

  * Called when a player has been kicked from the server
  * No return behavior

     void OnUserKicked(IPlayer player, string reason)
    {
        Puts($"Player {player.Name} ({player.Id}) was kicked");
    }

##### __**OnExperimentEnd**

  * Called when an experiment is about to end
  * Returning a non-null value overrides defualt behaviour.

     object OnExperimentEnd(Workbench workbench)
    {
        Puts("OnExperimentEnd works!");
        return null;
    }

##### __**OnExperimentEnded**

  * Called after the experiment has finished
  * No return behaviour

     void OnExperimentEnded(Workbench workbench)
    {
        Puts("OnExperimentEnded works!");
    }

##### __**OnUserBanned**

  * Called when a player has been banned from the server
  * Will have reason available if provided
  * No return behavior

     void OnUserBanned(string name, string id, string ipAddress, string reason)
    {
        Puts($"Player {name} ({id}) at {ipAddress} was banned: {reason}");
    }

##### __**CanHideStash**

  * Called when a player tries to hide a stash
  * Returning a non-null value overrides default behavior

     object CanHideStash(BasePlayer player, StashContainer stash)
    {
        Puts("CanHideStash works!");
        return null;
    }

##### __**OnUserUnbanned**

  * Called when a player has been unbanned from the server
  * No return behavior

     void OnUserUnbanned(string name, string id, string ipAddress)
    {
        Puts($"Player {name} ({id}) at {ipAddress} was unbanned");
    }

##### __**CanLootPlayer**

  * Called when the player attempts to loot another player
  * Returning true or false overrides default behavior

     bool CanLootPlayer(BasePlayer target, BasePlayer looter)
    {
        Puts("CanLootPlayer works!");
        return true;
    }

##### __**CanLootEntity**

  * Called when the player starts looting a DroppedItemContainer, LootableCorpse, ResourceContainer, BaseRidableAnimal, or StorageContainer entity
  * Returning a non-null value overrides default behavior

     object CanLootEntity(BasePlayer player, DroppedItemContainer container)
    {
        Puts("CanLootEntity works!");
        return null;
    }

     object CanLootEntity(BasePlayer player, LootableCorpse  corpse)
    {
        Puts("CanLootEntity works!");
        return null;
    }

     object CanLootEntity(BasePlayer player, ResourceContainer container)
    {
        Puts("CanLootEntity works!");
        return null;
    }

     object CanLootEntity(BasePlayer player, BaseRidableAnimal animal)
    {
        Puts("CanLootEntity works!");
        return null;
    }

     object CanLootEntity(BasePlayer player, StorageContainer container)
    {
        Puts("CanLootEntity works!");
        return null;
    }

##### __**CanMountEntity**

  * Called when the player attempts to mount an entity
  * Returning a non-null value overrides default behavior

     object CanMountEntity(BasePlayer player, BaseMountable entity)
    {
        Puts("CanMountEntity works!");
        return null;
    }

##### __**CanPickupEntity**

  * Called when a player attempts to pickup a deployed entity (AutoTurret, BaseMountable, BearTrap, DecorDeployable, Door, DoorCloser, ReactiveTarget, SamSite, SleepingBag, SpinnerWheel, StorageContainer, etc.)
  * Returning true or false overrides default behavior

     bool CanPickupEntity(BasePlayer player, BaseEntity entity)
    {
        Puts("CanPickupEntity works!");
        return true;
    }

##### __**CanPickupLock**

  * Called when a player attempts to pickup a lock
  * Returning true or false overrides default behavior

     bool CanPickupLock(BasePlayer player, BaseLock baseLock)
    {
        Puts("CanPickupLock works!");
        return true;
    }

##### __**CanRenameBed**

  * Called when the player attempts to rename a bed or sleeping bag
  * Returning a non-null value overrides default behavior

     object CanRenameBed(BasePlayer player, SleepingBag bed, string bedName)
    {
        Puts("CanRenameBed works!");
        return null;
    }

##### __**CanResearchItem**

  * Called when the player attempts to research an item
  * Returning a non-null value overrides default behavior

     object CanResearchItem(BasePlayer player, Item targetItem)
    {
        Puts("CanResearchItem works!");
        return null;
    }

##### __**CanUseLockedEntity**

  * Called when the player tries to use an entity that is locked
  * Returning true or false overrides default behavior

     bool CanUseLockedEntity(BasePlayer player, BaseLock baseLock)
    {
        Puts("CanUseLockedEntity works!");
        return true;
    }

##### __**CanSeeStash**

  * Called when a player is looking at a hidden stash
  * Returning a non-null value overrides default behavior

     object CanSeeStash(BasePlayer player, StashContainer stash)
    {
        Puts("CanSeeStash works!");
        return null;
    }

##### __**OnStashExposed**

  * Called when a player reveals a hidden stash
  * No return behavior

     void OnStashExposed(StashContainer stash, BasePlayer player)
    {
        Puts("OnStashExposed works");
    }

##### __**OnStashHidden**

  * Called when a player hides a stash
  * No return behavior

     void OnStashHidden(StashContainer stash, BasePlayer player)
    {
        Puts("OnStashHidden works!");
    }

##### __**CanSetBedPublic**

  * Called when a player tries to set a bed public
  * Returning a non-null value overrides default behavior

     object CanSetBedPublic(BasePlayer player, SleepingBag bed)
    {
        Puts("CanSetBedPublic works!");
        return null;
    }

##### __**CanUnlock**

  * Called when the player tries to unlock a keylock or codelock
  * Returning a non-null value overrides default behavior

     object CanUnlock(BasePlayer player, BaseLock baseLock)
    {
        Puts("CanUnlock works!");
        return null;
    }

##### __**CanUseMailbox**

  * Called when the player tries to use a mailbox
  * Returning true or false overrides default behavior

     bool CanUseMailbox(BasePlayer player, Mailbox mailbox)
    {
        Puts("CanUseMailbox works!");
        return true;
    }

##### __**CanUseUI**

  * Called when the player attempts to use a custom UI
  * Returning a non-null value overrides default behavior

     object CanUseUI(BasePlayer player, string json)
    {
        Puts("CanUseUI works!");
        return null;
    }

##### __**CanWearItem**

  * Called when the player attempts to equip an item
  * Returning a non-null value overrides default behavior

     object CanWearItem(PlayerInventory inventory, Item item, int targetSlot)
    {
        Puts("CanWearItem works!");
        return null;
    }

##### __**OnClientAuth**

  * Called when the player is giving server connection authorization information
  * No return behavior

     void OnClientAuth(Connection connection)
    {
        Puts("OnClientAuth works!");
    }

##### __**OnDestroyUI**

  * Called when a custom UI is destroyed for the player
  * No return behavior

     void OnDestroyUI(BasePlayer player, string json)
    {
        Puts("OnDestroyUI works!");
    }

##### __**OnFindSpawnPoint**

  * Useful for controlling player spawnpoints (like making all spawns occur in a set area)
  * Return a BasePlayer.SpawnPoint object to use that spawnpoint

     void OnFindSpawnPoint(BasePlayer player)
    {
        Puts("OnFindSpawnPoint works!");
    }

##### __**OnLootEntity**

  * Called when the player starts looting an entity
  * No return behavior

     void OnLootEntity(BasePlayer player, BaseEntity entity)
    {
        Puts("OnLootEntity works!");
    }

##### __**OnLootEntityEnd**

  * Called when the player stops looting an entity
  * No return behavior

     void OnLootEntityEnd(BasePlayer player, BaseCombatEntity entity)
    {
        Puts("OnLootEntityEnd works!");
    }

##### __**OnLootItem**

  * Called when the player starts looting an item
  * No return behavior

     void OnLootItem(PlayerLoot playerLoot, Item item)
    {
        Puts("OnLootItem works!");
    }

##### __**OnLootPlayer**

  * Called when the player starts looting another player
  * No return behavior

     void OnLootPlayer(BasePlayer player, BasePlayer target)
    {
        Puts("OnLootPlayer works!");
    }

##### __**OnPlayerAttack**

  * Useful for modifying an attack before it goes out
  * hitInfo.HitEntity should be the entity that this attack would hit
  * Returning a non-null value overrides default behavior

     object OnPlayerAttack(BasePlayer attacker, HitInfo info)
    {
        Puts("OnPlayerAttack works!");
        return null;
    }

##### __**OnPlayerBanned**

  * Called when the player is banned (Facepunch, EAC, server ban, etc.)
  * No return behavior

     void OnPlayerBanned(string name, ulong id, string address, string reason)
    {
        Puts("OnPlayerBanned works!");
    }

##### __**OnPlayerChat**

  * Called when the player sends chat to the server
  * Returning a non-null value overrides default behavior of chat, not commands

     object OnPlayerChat(BasePlayer player, string message, Chat.ChatChannel channel)
    {
        Puts("OnPlayerChat works!");
        return null;
    }

##### __**OnPlayerConnected**

  * Called after the player object is created, but before the player has spawned
  * No return behavior

     void OnPlayerConnected(BasePlayer player)
    {
        Puts("OnPlayerConnected works!");
    }

##### __**OnPlayerDeath**

  * Called when the player is about to die
  * HitInfo may be null sometimes
  * Returning a non-null value overrides default behavior

     object OnPlayerDeath(BasePlayer player, HitInfo info)
    {
        Puts("OnPlayerDeath works!");
        return null;
    }

##### __**OnPlayerDisconnected**

  * Called after the player has disconnected from the server
  * No return behavior

     void OnPlayerDisconnected(BasePlayer player, string reason)
    {
        Puts("OnPlayerDisconnected works!");
    }

##### __**OnPlayerDropActiveItem**

  * Called when the player drops their active held item
  * No return behavior

     void OnPlayerDropActiveItem(BasePlayer player, Item item)
    {
        Puts("OnPlayerDropActiveItem works!");
    }

##### __**OnPlayerHealthChange**

  * Called just before the player's health changes
  * Returning a non-null value cancels the health change

     object OnPlayerHealthChange(BasePlayer player, float oldValue, float newValue)
    {
        Puts("OnPlayerHealthChange works!");
        return null;
    }

##### __**OnPlayerInput**

  * Called when input is received from a connected client
  * No return behavior

     void OnPlayerInput(BasePlayer player, InputState input)
    {
        Puts("OnPlayerInput works!");
    }

##### __**OnPlayerKicked**

  * Called after the player is kicked from the server
  * No return behavior

     void OnPlayerKicked(BasePlayer player, string reason)
    {
        Puts("OnPlayerKicked works!");
    }

##### __**OnPlayerLand**

  * Called just before the player lands on the ground
  * Returning a non-null value overrides default behavior

     object OnPlayerLand(BasePlayer player, float num)
    {
        Puts("OnPlayerLand works!");
        return null;
    }

##### __**OnPlayerLanded**

  * Called when the player landed on the ground
  * No return behavior

     void OnPlayerLanded(BasePlayer player, float num)
    {
        Puts("OnPlayerLanded works!");
    }

##### __**OnPlayerLootEnd**

  * Called when the player stops looting
  * No return behavior

     void OnPlayerLootEnd(PlayerLoot inventory)
    {
        Puts("OnPlayerLootEnd works!");
    }

##### __**OnPlayerMetabolize**

  * Called after the player's metabolism has been changed
  * No return behavior

     void OnPlayerMetabolize(PlayerMetabolism metabolism, BaseCombatEntity entity, float delta)
    {
        Puts("OnPlayerMetabolize works!");
    }

##### __**OnPlayerRecover**

  * Called when the player is about to recover from the 'wounded' state
  * Returning a non-null value overrides default behavior

     object OnPlayerRecover(BasePlayer player)
    {
        Puts("OnPlayerRecover works!");
        return null;
    }

##### __**OnPlayerRespawn**

  * Called when a player is attempting to respawn
  * Returning a BasePlayer.SpawnPoint (takes a position and rotation), overrides the respawn location, for the variant that receives a BasePlayer.SpawnPoint argument
  * Returning a SleepingBag overrides the respawn location, for the variant that receives a SleepingBag argument

     object OnPlayerRespawn(BasePlayer player, BasePlayer.SpawnPoint spawnPoint)
    {
        Puts("OnPlayerRespawn works!");
        return null;
    }

     object OnPlayerRespawn(BasePlayer player, SleepingBag sleepingBag)
    {
        Puts("OnPlayerRespawn works!");
        return null;
    }

##### __**OnPlayerRespawned**

  * Called when the player has respawned (specifically when they click the "Respawn" button)
  * ONLY called after the player has transitioned from dead to not-dead, so not when they're waking up
  * This means it's possible for the player to connect and disconnect from a server without OnPlayerRespawned ever triggering for them
  * No return behavior

     void OnPlayerRespawned(BasePlayer player)
    {
        Puts("OnPlayerRespawned works!");
    }

##### __**OnRespawnInformationGiven**

  * Called when a player is about to be sent respawn information
  * No return behavior

     void OnRespawnInformationGiven(BasePlayer player, RespawnInformation respawnInformation)
    {
        Puts("OnRespawnInformationGiven works!");
    }

##### __**OnPlayerSleep**

  * Called when the player is about to go to sleep
  * Returning a non-null value overrides default behavior

     object OnPlayerSleep(BasePlayer player)
    {
        Puts("OnPlayerSleep works!");
        return null;
    }

##### __**OnPlayerSleepEnded**

  * Called when the player awakes
  * No return behavior

     void OnPlayerSleepEnded(BasePlayer player)
    {
        Puts("OnPlayerSleepEnded works!");
    }

##### __**OnPlayerSpawn**

  * Called when a player is attempting to spawn for the first time
  * Returning true overrides default behavior

     object OnPlayerSpawn(BasePlayer player)
    {
        Puts("OnPlayerSpawn works!");
        return null;
    }

##### __**OnPlayerSpectate**

  * Called when the player starts spectating
  * Returning a non-null value overrides default behavior

     object OnPlayerSpectate(BasePlayer player, string spectateFilter)
    {
        Puts("OnPlayerSpectate works!");
        return null;
    }

##### __**OnPlayerSpectateEnd**

  * Called when the player stops spectating
  * Returning a non-null value stops the spectate from ending

     object OnPlayerSpectateEnd(BasePlayer player, string spectateFilter)
    {
        Puts("OnPlayerSpectateEnd works!");
        return null;
    }

##### __**OnPlayerTick**

  * Returning a non-null value overrides default behavior

     object OnPlayerTick(BasePlayer player, PlayerTick msg, bool wasPlayerStalled)
    {
        Puts("OnPlayerTick works!");
        return null;
    }

##### __**OnPlayerViolation**

  * Called when the player triggers an anti-hack violation
  * Returning a non-null value overrides default behavior

     object OnPlayerViolation(BasePlayer player, AntiHackType type, float amount)
    {
        Puts("OnPlayerViolation works!");
        return null;
    }

##### __**OnPlayerVoice**

  * Called when the player uses the in-game voice chat
  * Returning a non-null value overrides default behavior

     object OnPlayerVoice(BasePlayer player, Byte[] data)
    {
        Puts("OnPlayerVoice works!");
        return null;
    }

##### __**OnPlayerWound**

  * Called when the player is about to go down to the 'wounded' state
  * source might be null, check it before use
  * Returning a non-null value cancels the wounded state

     object OnPlayerWound(BasePlayer player, HitInfo info)
    {
        Puts("OnPlayerWound works!");
        return null;
    }

##### __**OnRunPlayerMetabolism**

  * Called before a metabolism update occurs for the specified player
  * Metabolism update consists of managing the player's temperature, health etc.
  * You can use this to turn off or change certain aspects of the metabolism, either by editing values before returning, or taking complete control of the method
  * Returning a non-null value cancels the update

     object OnRunPlayerMetabolism(PlayerMetabolism metabolism, BasePlayer player, float delta)
    {
        Puts("OnRunPlayerMetabolism works!");
        return null;
    }

##### __**OnUserApprove**

  * Used by RustCore and abstracted into CanClientLogin
  * Returning a non-null value overrides default behavior, plugin should call Reject if it does this

     object OnUserApprove(Network.Connection connection)
    {
        Puts("OnUserApprove works!");
        return null;
    }

##### __**OnDefaultItemsReceive**

  * Called when a player is about to receive default items
  * Returning a non-null value overrides default behavior

     object OnDefaultItemsReceive(PlayerInventory inventory)
    {
        Puts("OnDefaultItemsReceive works!");
        return null;
    }

##### __**OnDefaultItemsReceived**

  * Called after a player has received default items
  * Returning a non-null value overrides default behavior

     void OnDefaultItemsReceived(PlayerInventory inventory)
    {
        Puts("OnDefaultItemsReceived works!");
    }

##### __**OnAnalysisComplete**

  * Called right after a player completes a survey crater analysis
  * No return behavior

     void OnAnalysisComplete(SurveyCrater surveyCrater, BasePlayer player)
    {
        Puts("OnAnalysisComplete works!");
    }

##### __**OnNpcConversationStart**

  * Called when a player tries to start a conversation with an NPC
  * Returning a non-null value overrides default behavior

     object OnNpcConversationStart(NPCTalking npcTalking, BasePlayer player, ConversationData conversationData)
    {
        Puts("OnNpcConversationStart works!");
        return null;
    }

##### __**OnNpcConversationRespond**

  * Called when a player chooses an NPC conversation response
  * Returning a non-null value overrides default behavior

     object OnNpcConversationRespond(NPCTalking npcTalking, BasePlayer player, ConversationData conversationData, ConversationData.ResponseNode responseNode)
    {
        Puts("OnNpcConversationRespond works!");
        return null;
    }

##### __**OnNpcConversationResponded**

  * Called right after a player's chosen NPC conversation response has been processed
  * No return behavior

     void OnNpcConversationResponded(NPCTalking npcTalking, BasePlayer player, ConversationData conversationData, ConversationData.ResponseNode responseNode)
    {
        Puts("OnNpcConversationResponded works!");
    }

##### __**OnNpcConversationEnded**

  * Called right after a player has ended an NPC conversation
  * No return behavior

     void OnNpcConversationEnded(NPCTalking npcTalking, BasePlayer player)
    {
        Puts("OnNpcConversationEnded works!");
    }

##### __**OnNetworkGroupEntered**

  * Called after a player has entered a network group
  * No return behavior

     void OnNetworkGroupEntered(BasePlayer player, Network.Visibility.Group group)
    {
        Puts("OnNetworkGroupEntered works!");
    }

##### __**OnNetworkGroupLeft**

  * Called after a player has left a network group
  * No return behavior

     void OnNetworkGroupLeft(BasePlayer player, Network.Visibility.Group group)
    {
        Puts("OnNetworkGroupLeft works!");
    }

##### __**OnDemoRecordingStart**

  * Called right before a demo of a player starts recording
  * Returning a non-null value overrides default behavior

     object OnDemoRecordingStart(string filename, BasePlayer player)
    {
        Puts("OnDemoRecordingStart works!");
        return null;
    }

##### __**OnDemoRecordingStarted**

  * Called after a demo of a player has started recording
  * No return behavior

     void OnDemoRecordingStarted(string filename, BasePlayer player)
    {
        Puts("OnDemoRecordingStarted works!");
    }

##### __**OnDemoRecordingStop**

  * Called right before a demo of a player stops recording
  * Returning a non-null value overrides default behavior

     object OnDemoRecordingStop(string filename, BasePlayer player)
    {
        Puts("OnDemoRecordingStop works!");
        return null;
    }

##### __**OnDemoRecordingStopped**

  * Called after a demo of a player has stopped recording
  * No return behavior

     void OnDemoRecordingStopped(string filename, BasePlayer player)
    {
        Puts("OnDemoRecordingStopped works!");
    }

##### __**OnLootNetworkUpdate**

  * Called when a player is trying to loot a container or a container they are looting has changed its contents
  * Returning a non-null value overrides default behavior

     object OnLootNetworkUpdate(PlayerLoot loot)
    {
        Puts("OnLootNetworkUpdate works!");
        return null;
    }

##### __**OnInventoryNetworkUpdate**

  * Called after a player's inventory contents have changed, before it is sent over the network to one or more clients
  * Returning a non-null value overrides default behavior

     object OnInventoryNetworkUpdate(PlayerInventory inventory, ItemContainer container, ProtoBuf.UpdateItemContainer updateItemContainer, PlayerInventory.Type type, PlayerInventory.PlayerInventory.NetworkInventoryMode networkInventoryMode)
    {
        Puts("OnInventoryNetworkUpdate works!");
        return null;
    }

##### __**OnPlayerAddModifiers**

  * Called after a player consumes an item such as tea that is about to apply modifiers
  * Returning a non-null value overrides default behavior (prevents the modifiers from being applied)

     object OnPlayerAddModifiers(BasePlayer player, Item item, ItemModConsumable consumable)
    {
        Puts("OnPlayerAddModifiers works!");
        return null;
    }

##### __**OnThreatLevelUpdate**

  * Called when a player's threat level is about to be updated
  * Returning a non-null value cancels default behavior

     object OnThreatLevelUpdate(BasePlayer player)
    {
        Puts("OnThreatLevelUpdate works!");
        return null;
    }

##### __**CanUseGesture**

  * Called when a player attempts to use a gesture
  * Returning true or false overrides default behavior

     bool? CanUseGesture(BasePlayer player, GestureConfig gesture)
    {
        Puts("CanUseGesture works!");
        return null;
    }

##### __**OnCentralizedBanCheck**

  * Called when a player is about to be checked in the centralized ban database
  * Returning a non-null value cancels default behavior

     object OnCentralizedBanCheck(Network.Connection connection)
    {
        Puts("OnCentralizedBanCheck works!");
        return null;
    }

##### __**OnClientCommand**

  * Useful for intercepting players' commands before their handling
  * Called before OnPlayerCommand and OnUserCommand
  * Returning a non-null value overrides default behavior

     object OnClientCommand(Connection connection, string command)
    {
        Puts("OnClientCommand works!");
        return null;
    }

##### __**OnCorpsePopulate**

  * Called when an NPC player corpse is about to be populated with loot
  * Returning a BaseCorpse overrides default behavior

     BaseCorpse OnCorpsePopulate(BasePlayer npcPlayer, BaseCorpse corpse)
    {
        Puts("OnCorpsePopulate works!");
        return null;
    }

##### __**CanSetRelationship**

  * Called when a player's relationship with another is about to be updated
  * Returning true or false overrides default behavior

     bool? CanSetRelationship(BasePlayer player, BasePlayer otherPlayer, RelationshipManager.RelationshipType relationshipType, int weight)
    {
        Puts("CanSetRelationship works!");
        return null;
    }

## __Entity Hooks

#####  __**CanBradleyApcTarget**

  * Called when an APC targets an entity
  * Returning true or false overrides default behavior

     bool CanBradleyApcTarget(BradleyAPC apc, BaseEntity entity)
    {
        Puts("CanBradleyApcTarget works!");
        return true;
    }

##### __**OnNpcPlayerResume**

  * Useful for canceling the invoke of TryForceToNavmesh
  * Returning a non-null value cancels default behavior

     object OnNpcPlayerResume(NPCPlayerApex npc)
    {
        Puts("OnNpcPlayerResume works!");
        return null;
    }

##### __**OnNpcDestinationSet**

  * Useful for canceling the destination change on NPCs
  * Returning a non-null value cancels default behavior

     object OnNpcDestinationSet(NPCPlayerApex npc, Vector3 newPosition)
    {
        Puts("OnNpcDestinationSet works!");
        return null;
    }

##### __**OnNpcStopMoving**

  * Useful for denying the move stop of NPCs
  * Returning a non-null value cancels default behavior

     object OnNpcStopMoving(NPCPlayerApex npc)
    {
        Puts("OnNpcStopMoving works!");
        return null;
    }

##### __**OnEntityMarkHostile**

  * Useful for denying marking the entity hostile
  * Returning a non-null value cancels default behavior

     object OnEntityMarkHostile(BaseCombatEntity entity, float duration)
    {
        Puts("OnEntityMarkHostile works!");
        return null;
    }

##### __**CanEntityBeHostile**

  * Useful for overriding hostility of an entity
  * Returning a non-null value overrides default behavior

     object CanEntityBeHostile(BaseCombatEntity entity)
    {
        Puts("CanEntityBeHostile works!");
        return null;
    }

##### __**CanSamSiteShoot**

  * Useful for canceling the shoot of SamSite
  * Returning a non-null value cancels default behavior

     object CanSamSiteShoot(SamSite samSite)
    {
        Puts("CanSamSiteShoot works!");
        return null;
    }

##### __**OnDieselEngineToggle**

  * Called when a player is trying to toggle diesel engine
  * Returning a non-null value cancels default behavior

     object OnDieselEngineToggle(DieselEngine engine, BasePlayer player)
    {
        Puts("OnDieselEngineToggle works!");
        return null;
    }

##### __**OnDieselEngineToggled**

  * Called when diesel engine is toggled
  * No return behavior

     void OnDieselEngineToggled(DieselEngine engine)
    {
        Puts("OnDieselEngineToggled works!");
    }

##### __**OnExcavatorMiningToggled**

  * Called when excavator mining arm is toggled
  * No return behavior

     void OnExcavatorMiningToggled(ExcavatorArm arm)
    {
        Puts("OnExcavatorMiningToggled works!");
    }

##### __**OnExcavatorGather**

  * Called right before moving gathered resource to container
  * Returning a non-null value cancels default behavior

     object OnExcavatorGather(ExcavatorArm arm, Item item)
    {
        Puts("OnExcavatorGather works!");
        return null;
    }

##### __**OnExcavatorResourceSet**

  * Called when a player is trying to set a new resource target
  * Returning a non-null value cancels default behavior

     object OnExcavatorResourceSet(ExcavatorArm arm, string resourceName, BasePlayer player)
    {
        Puts("OnExcavatorResourceSet works!");
        return null;
    }

##### __**OnInputUpdate**

  * Called when an input of IOEntity is updated
  * Returning a non-null value cancels default behavior

     object OnInputUpdate(IOEntity entity, int inputAmount, int slot)
    {
        Puts("OnInputUpdate works!");
        return null;
    }

##### __**OnOutputUpdate**

  * Called when outputs of IOEntity are updated
  * Returning a non-null value cancels default behavior

     object OnOutputUpdate(IOEntity entity)
    {
        Puts("OnOutputUpdate works!");
        return null;
    }

##### __**OnButtonPress**

  * Called when a player is trying to press a button
  * Returning a non-null value cancels default behavior

     object OnButtonPress(PressButton button, BasePlayer player)
    {
        Puts("OnButtonPress works!");
        return null;
    }

##### __**OnShopAcceptClick**

  * Called when a player is trying to accept a trade in ShopFront
  * Returning a non-null value cancels default behavior

     object OnShopAcceptClick(ShopFront entity, BasePlayer player)
    {
        Puts("OnShopAcceptClick works!");
        return null;
    }

##### __**OnShopCancelClick**

  * Called when a player is cancelling a trade
  * Returning a non-null value cancels default behavior

     object OnShopCancelClick(ShopFront entity, BasePlayer player)
    {
        Puts("OnShopCancelClick works!");
        return null;
    }

##### __**OnShopCompleteTrade**

  * Called before the trade is completed in ShopFront
  * Returning a non-null value cancels default behavior

     object OnShopCompleteTrade(ShopFront entity)
    {
        Puts("OnShopCompleteTrade works!");
        return null;
    }

##### __**OnSamSiteTarget**

  * Called before last target visible time is updated
  * Returning a non-null value cancels default behavior

     object OnSamSiteTarget(SamSite samSite, BaseCombatEntity target)
    {
        Puts("OnSamSiteTarget works!");
        return null;
    }

##### __**OnHelicopterStrafeEnter**

  * Called when helicopter is entering strafe
  * Returning a non-null value cancels default behavior

     object OnHelicopterStrafeEnter(PatrolHelicopterAI helicopter, Vector3 strafePosition)
    {
        Puts("OnHelicopterStrafeEnter works!");
        return null;
    }

##### __**OnSupplyDropLanded**

  * Called after Supply Drop has landed
  * No return behavior

     void OnSupplyDropLanded(SupplyDrop entity)
    {
        Puts("OnSupplyDropLanded works!");
    }

##### __**OnEntityStabilityCheck**

  * Called when stability of an entity is checked
  * Returning a non-null value cancels default behavior

     object OnEntityStabilityCheck(StabilityEntity entity)
    {
        Puts("OnEntityStabilityCheck works!");
        return null;
    }

##### __**OnBuildingPrivilege**

  * Useful for overriding a building privilege on specific entities and etc.
  * Returning BuildingPrivlidge value overrides default behavior

     BuildingPrivlidge OnBuildingPrivilege(BaseEntity entity, OBB obb)
    {
        Puts($"Getting a building privilege for {entity.ShortPrefabName}!");
        return null;
    }

##### __**OnHorseLead**

  * Called when a player tries to lead a horse
  * Returning a non-null value overrides default behavior

     object OnHorseLead(BaseRidableAnimal animal, BasePlayer player)
    {
        Puts($"{player.displayName} tries to lead {animal.ShortPrefabName}");
        return null;
    }

##### __**OnHorseHitch**

  * Called just before setting the hitch
  * Returning a non-null value overrides default behavior

     object OnHorseHitch(RidableHorse horse, HitchTrough hitch)
    {
        Puts("OnHorseHitch works!");
        return null;
    }

##### __**OnWireConnect**

  * Useful for preventing a wire to connect
  * Returning a non-null value cancels default behavior

     object OnWireConnect(BasePlayer player, IOEntity entity1, int inputs, IOEntity entity2, int outputs)
    {
        Puts("OnWireConnect works!");
        return null;
    }

##### __**OnWireClear**

  * Useful for preventing clearing wires
  * Returning a non-null value cancels default behavior

     object OnWireClear(BasePlayer player, IOEntity entity1, int connected, IOEntity entity2, bool flag)
    {
        Puts("OnWireClear works!");
        return null;
    }

##### __**OnReactiveTargetReset**

  * Called after the reactive target is reset
  * No return behaviour

     void OnReactiveTargetReset(ReactiveTarget target)
    {
        Puts("OnReactiveTargetReset works!");
    }

##### __**OnMlrsFire**

  * Called just before the MLRS is fired.
  * Returning a non-null value overrides default behaviour.

     object OnMlrsFire(MLRS mlrs, BasePlayer player)
    {
        Puts("OnMlrsFire works!");
        return null;
    }

##### __**OnMlrsFired**

  * Called just after the MLRS has been fired.
  * No return behaviour.

     void OnMlrsFired(MLRS mlrs, BasePlayer player)
    {
        Puts("OnMlrsFired works!");
    }

##### __**OnTurretAssign**

  * Called when a player attempts to authorize another player on a turret
  * Returning a non-null value cancels default behavior

     object OnTurretAssign(AutoTurret turret, ulong targetId, BasePlayer initiator)
    {
        Puts("{targetId} has been authorized on a turret by {initiator.displayName}");
        return null;
    }

##### __**OnTurretAssigned**

  * Called when a player has been authorized on a turret by another player
  * No return behaviour

     void OnTurretAssigned(AutoTurret turret, ulong targetId, BasePlayer initiator)
    {
        Puts($"{targetId} has been authorized on a turret by {initiator.displayName});
    }

##### __**CanHelicopterDropCrate**

  * Called when a CH47 helicopter attempts to drop a crate
  * Returning true or false overrides default behavior

     bool CanHelicopterDropCrate(CH47HelicopterAIController heli)
    {
        Puts("CanHelicopterDropCrate works!");
        return true;
    }

##### __**OnEngineLoadoutRefresh**

  * Called before engine loadout data is refreshed
  * Returning a non-null value overrides default behaviour

     object OnEngineLoadoutRefresh(EngineStorage storage)
    {
        Puts("OnEngineLoadoutRefresh works!");
        return null;
    }

##### __**CanHelicopterStrafe**

  * Called when a patrol helicopter attempts to strafe
  * Returning true or false overrides default behavior

     bool CanHelicopterStrafe(PatrolHelicopterAI heli)
    {
        Puts("CanHelicopterStrafe works!");
        return true;
    }

##### __**CanHelicopterStrafeTarget**

  * Called when a patrol helicopter attempts to target a player to attack while strafing
  * Returning true or false overrides default behavior

     bool CanHelicopterStrafeTarget(PatrolHelicopterAI entity, BasePlayer target)
    {
        Puts("CanHelicopterStrafeTarget works!");
        return true;
    }

##### __**CanHelicopterTarget**

  * Called when a patrol helicopter attempts to target a player to attack
  * Returning true or false overrides default behavior

     bool CanHelicopterTarget(PatrolHelicopterAI heli, BasePlayer player)
    {
        Puts("CanHelicopterTarget works!");
        return true;
    }

##### __**CanHelicopterUseNapalm**

  * Called when a patrol helicopter attempts to use napalm
  * Returning true or false overrides default behavior

     bool CanHelicopterUseNapalm(PatrolHelicopterAI heli)
    {
        Puts("CanHelicopterUseNapalm works!");
        return true;
    }

##### __**CanNetworkTo**

  * Called when an entity attempts to network with a player
  * For better performance, avoid using heavy calculations in this hook.
  * Returning true or false overrides default behavior

     bool CanNetworkTo(BaseNetworkable entity, BasePlayer target)
    {
        Puts("CanNetworkTo works!");
        return true;
    }

##### __**OnHelicopterRetire**

  * Called before the patrol helicopter starts leaving the map.
  * Returning a non-null value overrides default behaviour.

     object OnHelicopterRetire(PatrolHelicopterAI helicopter)
    {
        Puts("OnHelicopterRetire works!");
        return null;
    }

##### __**CanNpcAttack**

  * Called when a NPC attempts to attack another entity
  * Returning true or false overrides default behavior

     bool CanNpcAttack(BaseNpc npc, BaseEntity target)
    {
        Puts("CanNpcAttack works!");
        return true;
    }

##### __**CanNpcEat**

  * Called when a NPC attempts to eat another entity
  * Returning true or false overrides default behavior

     bool CanNpcEat(BaseNpc npc, BaseEntity target)
    {
        Puts("CanNpcEat works!");
        return true;
    }

##### __**CanRecycle**

  * Called when the recycler attempts to recycle an item
  * Returning true or false overrides default behavior

     bool CanRecycle(Recycler recycler, Item item)
    {
        Puts("CanRecycle works!");
        return true;
    }

##### __**OnAirdrop**

  * Called when an airdrop has been called
  * No return behavior

     void OnAirdrop(CargoPlane plane, Vector3 dropPosition)
    {
        Puts("OnAirdrop works!");
    }

##### __**OnBradleyApcInitialize**

  * Called when an APC initializes
  * Returning a non-null value overrides default behavior

     object OnBradleyApcInitialize(BradleyAPC apc)
    {
        Puts("OnBradleyApcInitialize works!");
        return null;
    }

##### __**OnBradleyApcHunt**

  * Called when an APC starts hunting
  * Returning a non-null value overrides default behavior

     object OnBradleyApcHunt(BradleyAPC apc)
    {
        Puts("OnBradleyApcHunt works!");
        return null;
    }

##### __**OnBradleyApcPatrol**

  * Called when an APC is starts patrolling
  * Returning a non-null value overrides default behavior

     object OnBradleyApcPatrol(BradleyAPC apc)
    {
        Puts("OnBradleyApcPatrol works!");
        return null;
    }

##### __**OnContainerDropItems**

  * Called when a container is destroyed and all items are about to be dropped
  * Returning a non-null value overrides default behavior

     object OnContainerDropItems(ItemContainer container)
    {
        Puts("OnContainerDropItems works!");
        return null;
    }

##### __**OnCrateDropped**

  * Called when a locked crate from the CH47 (Chinook) has dropped
  * No return behavior

     void OnCrateDropped(HackableLockedCrate crate)
    {
        Puts("OnCrateDropped works!");
    }

##### __**OnCrateHack**

  * Called when a player starts hacking a locked crate
  * No return behavior

     void OnCrateHack(HackableLockedCrate crate)
    {
        Puts("OnCrateHack works!");
    }

##### __**OnCrateHackEnd**

  * Called when a player stops hacking a locked crate
  * No return behavior

     void OnCrateHackEnd(HackableLockedCrate crate)
    {
        Puts("OnCrateHackEnd works!");
    }

##### __**OnCrateLanded**

  * Called when a locked crate from the CH47 (Chinook) has landed
  * No return behavior

     void OnCrateLanded(HackableLockedCrate crate)
    {
        Puts("OnCrateLanded works!");
    }

##### __**OnEntityDeath**

  * HitInfo might be null, check it before use
  * Editing hitInfo has no effect because the death has already happened
  * No return behavior

     void OnEntityDeath(BaseCombatEntity entity, HitInfo info)
    {
        Puts("OnEntityDeath works!");
    }

##### __**OnEntityDismounted**

  * Called when an entity is dismounted by a player
  * No return behavior

     void OnEntityDismounted(BaseMountable entity, BasePlayer player)
    {
        Puts("OnEntityDismounted works!");
    }

##### __**OnEntityEnter**

  * Called when an entity enters a trigger (water area, radiation zone, hurt zone, etc.)
  * No return behavior

     void OnEntityEnter(TriggerBase trigger, BaseEntity entity)
    {
        Puts("OnEntityEnter works!");
    }

##### __**OnEntityGroundMissing**

  * Called when an entity (sleepingbag, sign, furnace,...) is going to be destroyed because the buildingblock it is on was removed
  * Returning a non-null value overrides default behavior

     object OnEntityGroundMissing(BaseEntity entity)
    {
        Puts("OnEntityGroundMissing works!");
        return null;
    }

##### __**OnEntityKill**

  * Called when an entity is destroyed
  * Returning a non-null value overrides default behavior

     object OnEntityKill(BaseNetworkable entity)
    {
        Puts("OnEntityKill works!");
        return null;
    }

##### __**OnEntityLeave**

  * Called when an entity leaves a trigger (water area, radiation zone, hurt zone, etc.)
  * No return behavior

     void OnEntityLeave(TriggerBase trigger, BaseEntity entity)
    {
        Puts("OnEntityLeave works!");
    }

##### __**OnEntityMounted**

  * Called when an entity is mounted by a player
  * No return behavior

     void OnEntityMounted(BaseMountable entity, BasePlayer player)
    {
        Puts("OnEntityMounted works!");
    }

##### __**OnEntitySpawned**

  * Called after any networked entity has spawned (including trees)
  * No return behavior

     void OnEntitySpawned(BaseNetworkable entity)
    {
        Puts("OnEntitySpawned works!");
    }

##### __**OnEntityTakeDamage**

  * Alternatively, modify the HitInfo object to change the damage
  * It should be okay to set the damage to 0, but if you don't return non-null, the player's client will receive a damage indicator (if entity is a BasePlayer)
  * HitInfo has all kinds of useful things in it, such as Weapon, damageProperties or damageTypes

     object OnEntityTakeDamage(BaseCombatEntity entity, HitInfo info)
    {
        Puts("OnEntityTakeDamage works!");
        return null;
    }

##### __**OnFireBallDamage**

  * Called when a fire ball does damage to another entity
  * No return behavior

     void OnFireBallDamage(FireBall fire, BaseCombatEntity entity, HitInfo info)
    {
        Puts("OnFireBallDamage works!");
    }

##### __**OnFireBallSpread**

  * Called when a fire ball fire spreads
  * No return behavior

     void OnFireBallSpread(FireBall ball, BaseEntity fire)
    {
        Puts("OnFireBallSpread works!");
    }

##### __**OnFlameExplosion**

  * Called when a flame explodes
  * No return behavior

     void OnFlameExplosion(FlameExplosive explosive, BaseEntity flame)
    {
        Puts("OnFlameExplosion works!");
    }

##### __**OnHelicopterAttack**

  * Called when a CH47 helicopter is being attacked
  * Returning a non-null value overrides default behavior

     object OnHelicopterAttack(CH47HelicopterAIController heli)
    {
        Puts("OnHelicopterAttack works!");
        return null;
    }

##### __**OnHelicopterDropCrate**

  * Called when a CH47 helicopter is dropping a crate
  * Returning a non-null value overrides default behavior

     object OnHelicopterDropCrate(CH47HelicopterAIController heli)
    {
        Puts("OnHelicopterDropCrate works!");
        return null;
    }

##### __**OnHelicopterDropDoorOpen**

  * Called when a CH47 helicopter is opening its drop door
  * Returning a non-null value overrides default behavior

     object OnHelicopterDropDoorOpen(CH47HelicopterAIController heli)
    {
        Puts("OnHelicopterDropDoorOpen works!");
        return null;
    }

##### __**OnHelicopterKilled**

  * Called when a CH47 helicopter is going to be killed
  * Returning a non-null value overrides default behavior

     object OnHelicopterKilled(CH47HelicopterAIController heli)
    {
        Puts("OnHelicopterKilled works!");
        return null;
    }

##### __**OnHelicopterOutOfCrates**

  * Called when a CH47 helicopter runs out of crates
  * Returning true or false overrides default behavior

     bool? OnHelicopterOutOfCrates(CH47HelicopterAIController heli)
    {
        Puts("OnHelicopterOutOfCrates works!");
        return null;
    }

##### __**OnHelicopterTarget**

  * Called when a helicopter turret attempts to target an entity
  * Returning a non-null value overrides default behavior

     object OnHelicopterTarget(HelicopterTurret turret, BaseCombatEntity entity)
    {
        Puts("OnHelicopterTarget works!");
        return null;
    }

##### __**OnLiftUse**

  * Called when a player calls a lift or procedural lift
  * Returning a non-null value overrides default behavior

     object OnLiftUse(Lift lift, BasePlayer player)
    {
        Puts("OnLiftUse works!");
        return null;
    }

     object OnLiftUse(ProceduralLift lift, BasePlayer player)
    {
        Puts("OnLiftUse works!");
        return null;
    }

##### __**OnLootSpawn**

  * Called when loot spawns in a container
  * Returning a non-null value overrides default behavior

     object OnLootSpawn(LootContainer container)
    {
        Puts("OnLootSpawn works!");
        return null;
    }

##### __**OnNpcTargetSense**

  * Called when an NPC becomes aware of another entity
  * Returning a non-null value overrides default behavior

     object OnNpcTargetSense(BaseEntity owner, BaseEntity entity, AIBrainSenses brainSenses)
    {
        Puts("OnNpcTargetSense works!");
        return null;
    }

##### __**OnNpcTarget**

  * Called when an NPC targets another entity
  * Returning a non-null value overrides default behavior

     object OnNpcTarget(BaseEntity npc, BaseEntity entity)
    {
        Puts("OnNpcTarget works!");
        return null;
    }

##### __**OnOvenToggle**

  * Called when an oven (Campfire, Furnace,...) is turned on or off
  * Returning a non-null value overrides default behavior

     object OnOvenToggle(BaseOven oven, BasePlayer player)
    {
        Puts("OnOvenToggle works!");
        return null;
    }

##### __**OnItemRecycle**

  * Called when an item is recycled in a recycler
  * Returning a non-null value overrides default behavior

     object OnItemRecycle(Item item, Recycler recycler)
    {
        Puts("OnItemRecycle works!");
        return null;
    }

##### __**OnOvenCook**

  * Called before an oven cooks an item
  * Returning a non-null value overrides default behavior

     object OnOvenCook(BaseOven oven, Item item)
    {
        Puts("OnOvenCook works!");
        return null;
    }

##### __**OnOvenCooked**

  * Called after an oven cooks an item
  * No return behavior

     void OnOvenCooked(BaseOven oven, Item item, BaseEntity slot)
    {
        Puts("OnOvenCooked works!");
    }

##### __**OnRecyclerToggle**

  * Called when a recycler is turned on or off
  * Returning a non-null value overrides default behavior

     object OnRecyclerToggle(Recycler recycler, BasePlayer player)
    {
        Puts("OnRecyclerToggle works!");
        return null;
    }

##### __**OnResourceDepositCreated**

  * Called when a resource deposit has been created
  * No return behavior

     void OnResourceDepositCreated(ResourceDepositManager.ResourceDeposit deposit)
    {
        Puts("OnResourceDepositCreated works!");
    }

##### __**OnShopCompleteTrade**

  * Called when a shopfront trade is complete
  * Returning a non-null value overrides default behavior

     object OnShopCompleteTrade(ShopFront shop, BasePlayer customer)
    {
        Puts("OnShopCompleteTrade works!");
        return null;
    }

##### __**OnTurretAuthorize**

  * Called when a player attempts to authorize on a turret
  * Returning a non-null value overrides default behavior

     object OnTurretAuthorize(AutoTurret turret, BasePlayer player)
    {
        Puts("OnTurretAuthorize works!");
        return null;
    }

##### __**OnTurretClearList**

  * Called when a player attempts to clear an autoturret's authorized list
  * Returning a non-null value overrides default behavior

     object OnTurretClearList(AutoTurret turret, BasePlayer player)
    {
        Puts("OnTurretClearList works!");
        return null;
    }

##### __**OnTurretDeauthorize**

  * Called when a player is deauthorized on an autoturret
  * Returning a non-null value overrides default behavior

     object OnTurretDeauthorize(AutoTurret turret, BasePlayer player)
    {
        Puts("OnTurretDeauthorize works!");
        return null;
    }

##### __**OnTurretModeToggle**

  * Called when the mode of an autoturrent is toggled
  * No return behavior

     void OnTurretModeToggle(AutoTurret turret)
    {
        Puts("OnTurretModeToggle works!");
    }

##### __**OnTurretShutdown**

  * Called when an autoturret is shut down
  * Returning a non-null value overrides default behavior

     object OnTurretShutdown(AutoTurret turret)
    {
        Puts("OnTurretShutdown works!");
        return null;
    }

##### __**OnTurretStartup**

  * Called when an autoturret starts up
  * Returning a non-null value overrides default behavior

     object OnTurretStartup(AutoTurret turret)
    {
        Puts("OnTurretStartup works!");
        return null;
    }

##### __**OnTurretTarget**

  * Called when an autoturret attempts to target an entity
  * Returning a non-null value overrides default behavior

     object OnTurretTarget(AutoTurret turret, BaseCombatEntity entity)
    {
        Puts("OnTurretTarget works!");
        return null;
    }

##### __**OnTurretToggle**

  * Called when an autoturret toggles powerstate (on/off)
  * Returning a non-null value overrides default behavior

     object OnTurretToggle(AutoTurret turret)
    {
        Puts("OnTurretToggle works!");
        return null;
    }

##### __**OnBigWheelWin**

  * Called before multiplier is applied.
  * Returning non-null value overrides default behaviour.

     object OnBigWheelWin(BigWheelGame bigWheel, Item scrap, BigWheelBettingTerminal terminal, int multiplier)
    {
        Puts("OnBigWheelWin works!");
        return null;
    }

##### __**OnBigWheelLoss**

  * Called when a specific item is lost on the big wheel game
  * No return behavior

     void OnBigWheelLoss(BigWheelGame wheel, Item item)
    {
        Puts("OnBigWheelLoss works!");
    }

##### __**OnSolarPanelSunUpdate**

  * Called when a solar panel updates the amount of energy it is getting from the sun
  * Returning a non-null value overrides default behavior

     object OnSolarPanelSunUpdate(SolarPanel panel, int currentEnergy)
    {
        Puts("OnSolarPanelSunUpdate works!");
        return null;
    }

##### __**OnBookmarkControl**

  * Called when a player tries to select a bookmark at a computer station
  * Returning a non-null value overrides default behavior

     object OnBookmarkControl(ComputerStation computerStation, BasePlayer player, string bookmarkName, IRemoteControllable remoteControllable)
    {
        Puts("OnBookmarkControl works!");
        return null;
    }

##### __**OnBookmarkControlStarted**

  * Called after a player has selected a bookmark at a computer station
  * No return behavior

     void OnBookmarkControlStarted(ComputerStation computerStation, BasePlayer player, string bookmarkName, IRemoteControllable remoteControllable)
    {
        Puts("OnBookmarkControlStarted works!");
    }

##### __**OnBookmarkDelete**

  * Called when a player tries to delete a bookmark at a computer station
  * Returning a non-null value overrides default behavior

     object OnBookmarkDelete(ComputerStation computerStation, BasePlayer player, string bookmarkName)
    {
        Puts("OnBookmarkDelete works!");
        return null;
    }

##### __**OnBookmarkAdd**

  * Called when a player tries to add a bookmark at a computer station
  * Returning a non-null value overrides default behavior

     object OnBookmarkAdd(ComputerStation computerStation, BasePlayer player, string bookmarkName)
    {
        Puts("OnBookmarkAdd works!");
        return null;
    }

##### __**OnBookmarksSendControl**

  * Called when a player is being sent a list of bookmarks for a computer station
  * Returning a non-null value overrides default behavior

     object OnBookmarksSendControl(ComputerStation computerStation, BasePlayer player, string bookmarks)
    {
        Puts("OnBookmarksSendControl works!");
        return null;
    }

##### __**OnBookmarkControlEnd**

  * Called when a player tries to stop viewing/controlling an entity at a computer station
  * Returning a non-null value overrides default behavior

     object OnBookmarkControlEnd(ComputerStation computerStation, BasePlayer player, BaseEntity controlledEntity)
    {
        Puts("OnBookmarkControlEnd works!");
        return null;
    }

##### __**OnBookmarkControlEnded**

  * Called after a player has stopped viewing/controlling an entity at a computer station
  * No return behavior

     void OnBookmarkControlEnded(ComputerStation computerStation, BasePlayer player, BaseEntity controlledEntity)
    {
        Puts("OnBookmarkControlEnded works!");
    }

##### __**OnRfBroadcasterAdd**

  * Called right before an object starts broadcasting an RF frequency
  * Returning a non-null value overrides default behavior

     object OnRfBroadcasterAdd(IRFObject obj, int frequency)
    {
        Puts("OnRfBroadcasterAdd works!");
        return null;
    }

##### __**OnRfBroadcasterAdded**

  * Called right after an object has started broadcasting an RF frequency
  * No return behavior

     void OnRfBroadcasterAdded(IRFObject obj, int frequency)
    {
        Puts("OnRfBroadcasterAdded works!");
    }

##### __**OnRfBroadcasterRemove**

  * Called right before an object stops broadasting an RF frequency
  * Returning a non-null value overrides default behavior

     object OnRfBroadcasterRemove(IRFObject obj, int frequency)
    {
        Puts("OnRfBroadcasterRemove works!");
        return null;
    }

##### __**OnRfBroadcasterRemoved**

  * Called right after an object has stopped broadcasting an RF frequency
  * No return behavior

     void OnRfBroadcasterRemoved(IRFObject obj, int frequency)
    {
        Puts("OnRfBroadcasterRemoved works!");
    }

##### __**OnRfListenerAdd**

  * Called right before an object starts listening to an RF frequency
  * Returning a non-null value overrides default behavior

     object OnRfListenerAdd(IRFObject obj, int frequency)
    {
        Puts("OnRfListenerAdd works!");
        return null;
    }

##### __**OnRfListenerAdded**

  * Called right after an object has started listening to an RF frequency
  * No return behavior

     void OnRfListenerAdded(IRFObject obj, int frequency)
    {
        Puts("OnRfListenerAdded works!");
    }

##### __**OnRfListenerRemove**

  * Called right before an object stops listening to an RF frequency
  * Returning a non-null value overrides default behavior

     object OnRfListenerRemove(IRFObject obj, int frequency)
    {
        Puts("OnRfListenerRemove works!");
        return null;
    }

##### __**OnRfListenerRemoved**

  * Called right after an object has stopped listening to an RF frequency
  * No return behavior

     void OnRfListenerRemoved(IRFObject obj, int frequency)
    {
        Puts("OnRfListenerRemoved works!");
    }

##### __**OnSleepingBagDestroy**

  * Called when a player tries to remove a sleeping bag from their respawn screen
  * Returning a non-null value overrides default behavior

     object OnSleepingBagDestroy(SleepingBag sleepingBag, BasePlayer player)
    {
        Puts("OnSleepingBagDestroy works!");
        return null;
    }

##### __**OnRfFrequencyChange**

  * Called when a player tries to change the frequency of an RF broadcaster or receiver
  * Returning a non-null value overrides default behavior
  * Useful for preventing particular reserved frequencies from being selected

     object OnRfFrequencyChange(IRFObject obj, int frequency, BasePlayer player)
    {
        Puts("OnRfFrequencyChange works!");
        return null;
    }

##### __**OnRfFrequencyChanged**

  * Called after a player has changed the frequency of an RF broadcaster or receiver
  * No return behavior

     void OnRfFrequencyChanged(IRFObject obj, int frequency, BasePlayer player)
    {
        Puts("OnRfFrequencyChanged works!");
    }

##### __**OnSleepingBagDestroyed**

  * Called after a player removes a sleeping bag from their respawn screen
  * No return behavior

     void OnSleepingBagDestroyed(SleepingBag sleepingBag, BasePlayer player)
    {
        Puts("OnSleepingBagDestroyed works!");
    }

##### __**OnNetworkSubscriptionsUpdate**

  * Called after the Rust has determined which network groups to subscribe a player to (`groupsToAdd`), and which network groups to unsubscribe the player from (`groupsToRemove`)
  * Returning a non-null value prevents Rust from applying the proposed subscription changes
  * This hook is useful for situations where you want to subscribe a player to a network group that is outside their network range -- To do so, you can prevent Rust from automatically unsubscribing them by removing that group from the `groupsToRemove` list

     object OnNetworkSubscriptionsUpdate(Network.Networkable networkable, List<Network.Visibility.Group> groupsToAdd, List<Network.Visibility.Group> groupsToRemove)
    {
        Puts("OnNetworkSubscriptionsUpdate works!");
    }

##### __**OnBookmarkInput**

  * Called when input is received from a player who is using a computer station with a bookmark selected
  * Returning a non-null value overrides default behavior

     object OnBookmarkInput(ComputerStation computerStation, BasePlayer player, InputState inputState)
    {
        Puts("OnBookmarkInput works!");
        return null;
    }

##### __**OnEntityControl**

  * Called when a player tries to remote control an entity
  * Returning true or false overrides default behavior

     object OnEntityControl(IRemoteControllable entity)
    {
        Puts("OnEntityControl works!");
        return null;
    }

##### __**OnTurretRotate**

  * Called when a player tries to rotate an auto turret
  * Returning a non-null value overrides default behavior

     object OnTurretRotate(AutoTurret turret, BasePlayer player)
    {
        Puts("OnTurretRotate works!");
        return null;
    }

##### __**OnCounterTargetChange**

  * Called when a player tries to change the target number of a power counter
  * Returning a non-null value overrides default behavior

     object OnCounterTargetChange(PowerCounter counter, BasePlayer player, int targetNumber)
    {
        Puts("OnCounterTargetChange works!");
        return null;
    }

##### __**OnCounterModeToggle**

  * Called when a player ties to toggle a power counter between modes
  * Returning a non-null value overrides default behavior

     object OnCounterModeToggle(PowerCounter counter, BasePlayer player, bool mode)
    {
        Puts("OnCounterModeToggle works!");
        return null;
    }

##### __**OnSwitchToggle**

  * Called when a player tries to switch and ElectricSwitch or FuelGenerator
  * Returning a non-null value cancels default behavior

     object OnSwitchToggle(IOEntity entity, BasePlayer player)
    {
        Puts("OnSwitchToggle works!");
        return null;
    }

##### __**OnSwitchToggled**

  * Called right after a player switches an ElectricSwitch or FuelGenerator
  * No return behavior

     void OnSwitchToggled(IOEntity entity, BasePlayer player)
    {
        Puts("OnSwitchToggled works!");
    }

##### __**OnEntityDestroy**

  * Called right before a CH47Helicopter or BradleyAPC is destroyed
  * Returning a non-null value overrides default behavior

     object OnEntityDestroy(BaseCombatEntity entity)
    {
        Puts("OnEntityDestroy works!");
        return null;
    }

##### __**OnElevatorButtonPress**

  * Called when a player presses a button on an elevator lift
  * Returning a non-null value overrides default behavior

     object OnElevatorButtonPress(ElevatorLift lift, BasePlayer player, Elevator.Direction direction, bool toTopOrBottom)
    {
        Puts("OnElevatorButtonPress works!");
        return null;
    }

##### __**OnElevatorCall**

  * Called when an elevator lift is called to a specific floor by electricity
  * Returning a non-null value overrides default behavior

     object OnElevatorCall(Elevator elevator, Elevator topElevator)
    {
        Puts("OnElevatorCall works!");
        return null;
    }

##### __**OnElevatorMove**

  * Called right before an elevator starts moving to the target floor
  * Returning a non-null value overrides default behavior

     object OnElevatorMove(Elevator topElevator, int targetFloor)
    {
        Puts("OnElevatorMove works!");
        return null;
    }

##### __**CanSwapToSeat**

  * Called when a player tries to switch seats, to determine whether each seat is eligible to be swapped to
  * Returning true or false overrides default behavior

     bool CanSwapToSeat(BasePlayer player, BaseMountable mountable)
    {
        Puts("CanSwapToSeat works!");
        return true;
    }

##### __**OnRidableAnimalClaim**

  * Called when a player tries to claim a horse
  * Returning a non-null value overrides default behavior

     object OnRidableAnimalClaim(BaseRidableAnimal animal, BasePlayer player)
    {
        Puts("OnRidableAnimalClaim works!");
        return null;
    }

##### __**OnRidableAnimalClaimed**

  * Called after a player has claimed a horse
  * No return behavior

     void OnRidableAnimalClaimed(BaseRidableAnimal animal, BasePlayer player)
    {
        Puts("OnRidableAnimalClaimed works!");
    }

##### __**OnEntitySaved**

  * Called after a BaseNetworkable has been saved to a ProtoBuf object and is about to be serialized to a network stream or network cache
  * No return behavior

     void OnEntitySaved(BaseNetworkable entity, BaseNetworkable.SaveInfo saveInfo)
    {
        Puts("OnEntitySaved works!");
    }

##### __**OnEntitySnapshot**

  * Called when an entity snapshot is about to be sent to a client connection
  * Returning a non-null value overrides default behavior

     object OnEntitySnapshot(BaseNetworkable entity, Connection connection)
    {
        Puts("OnEntitySnapshot works!");
        return null;
    }

##### __**OnIORefCleared**

  * Called after a wire has been disconnected from an electrical entity, such as when its connected entity was destroyed or when a player removed the wire
  * No return behavior

     void OnIORefCleared(IOEntity.IORef ioRef, IOEntity ioEntity)
    {
        Puts("OnIORefCleared works!");
    }

##### __**OnEntityFlagsNetworkUpdate**

  * Called after an entity's flags have been updated on the server, before they are sent over the network
  * Returning a non-null value overrides default behavior

     object OnEntityFlagsNetworkUpdate(BaseEntity entity)
    {
        Puts("OnEntityFlagsNetworkUpdate works!");
        return null;
    }

##### __**OnSupplyDropDropped**

  * Called right after a cargo plane has dropped a supply drop
  * No return behavior

     void OnSupplyDropDropped(SupplyDrop supplyDrop, CargoPlane cargoPlane)
    {
        Puts("OnSupplyDropDropped works!");
    }

##### __**OnCargoPlaneSignaled**

  * Called right after a supply signal has called a cargo plane
  * No return behavior

     void OnCargoPlaneSignaled(CargoPlane cargoPlane, SupplySignal supplySignal)
    {
        Puts("OnCargoPlaneSignaled works!");
    }

##### __**OnWaterPurify**

  * Called when salt water is about to be converted to fresh water in a water purifier
  * Returning a non-null value cancels default behavior

     object OnWaterPurify(WaterPurifier waterPurifier, float timeCooked)
    {
        Puts("OnWaterPurify works!");
        return null;
    }

##### __**OnWaterPurified**

  * Called after salt water has been converted to fresh water in a water purifier
  * No return behavior

     void OnWaterPurified(WaterPurifier waterPurifier, float timeCooked)
    {
        Puts("OnWaterPurified works!");
    }

##### __**OnSleepingBagValidCheck**

  * Called when determining if a sleeping bag is a valid respawn location for a player
  * Useful in conjunction with OnRespawnInformationGiven since a custom sleeping bag will need to pass this check
  * Returning true or false overrides default behavior

     bool? OnSleepingBagValidCheck(SleepingBag bag, ulong targetPlayerID, bool ignoreTimers)
    {
        Puts("OnSleepingBagValidCheck works!");
        return null;
    }

##### __**OnCCTVDirectionChange**

  * Called when a player attempts to change the direction of a CCTV camera to face them
  * Returning a non-null value cancels default behavior

     object OnCCTVDirectionChange(CCTV_RC camera, BasePlayer player)
    {
        Puts("OnCCTVDirectionChange works!");
        return null;
    }

##### __**OnWaterCollect**

  * Called when a water catcher is about to collect water
  * Returning a non-null value cancels default behavior

     object OnWaterCollect(WaterCatcher waterCatcher)
    {
        Puts("OnWaterCollect works!");
        return null;
    }

##### __**OnLiquidVesselFill**

  * Called when a player is attempting to fill a liquid vessel
  * Returning a non-null value cancels default behavior

     object OnLiquidVesselFill(BaseLiquidVessel liquidVessel, BasePlayer player, LiquidContainer facingLiquidContainer)
    {
        Puts("OnLiquidVesselFill works!");
        return null;
    }

##### __**OnLockerSwap**

  * Called when a player clicks the "Swap" button while viewing a locker interface
  * Returning non-null value overrides default behavior

     object OnLockerSwap(Locker locker, int startIndex, BasePlayer player)
    {
        Puts("OnLockerSwap works!");
        return null;
    }

##### __**CanLockerAcceptItem**

  * Called before an item is attempted to be placed inside a locker
  * Returning true or false overrides default behavior

     object CanLockerAcceptItem(Locker locker, Item item, int targetPos)
    {
        Puts("CanLockerAcceptItem works!");
        return null;
    }

## __Item Hooks

#####  __**CanAcceptItem**

  * Called when attempting to put an item in a container
  * Returning CanAcceptResult value overrides default behavior

    ItemContainer.CanAcceptResult? CanAcceptItem(ItemContainer container, Item item, int targetPos)
    {
        Puts("CanAcceptItem works!");
        return null;
    }

##### __**OnBackpackDrop**

  * Called just before a backpack is dropped from a player on death.
  * Return non-null to override default behaviour.

     object OnBackpackDrop(Item backpack, BasePlayer player)
    {
        Puts("OnBackpackDrop works!");
        return null;
    }

##### __**OnCardSwipe**

  * Called when a player is trying to swipe a card
  * Returning a non-null value cancels default behavior

     object OnCardSwipe(CardReader cardReader, Keycard card, BasePlayer player)
    {
        Puts("OnCardSwipe works!");
        return null;
    }

##### __**OnItemRemove**

  * Called before an item is destroyed
  * Return a non-null value stop item from being destroyed

     // Example that stops item from being destroyed

    object OnItemRemove(Item item)
    {
        Puts("OnItemRemove works!");
        return null;
    }

     // Example that does not stop item from being destroyed

    void OnItemRemove(Item item)
    {
        Puts("OnItemRemove works!");
    }

##### __**OnMapImageUpdated**

  * Called when player updates map item's image
  * Useful for executing any action when map image is updated
  * No return behavior

     void OnMapImageUpdated()
    {
        Puts("OnMapImageUpdated works!");
    }

##### __**CanDropActiveItem**

  * Called when a player attempts to drop their active item
  * Returning true or false overrides default behavior

     bool CanDropActiveItem(BasePlayer player)
    {
        Puts("CanDropActiveItem works!");
        return true;
    }

##### __**CanCombineDroppedItem**

  * Called when an item is dropped on another item
  * Returning a non-null value overwrites command arguments

     object CanCombineDroppedItem(DroppedItem item, DroppedItem targetItem)
    {
        Puts("CanCombineDroppedItem works!");
        return null;
    }

##### __**CanMoveItem**

  * Called when moving an item from one inventory slot to another
  * Returning a non-null value overrides default behavior

     object CanMoveItem(Item item, PlayerInventory playerLoot, ItemContainerId targetContainer, int targetSlot, int amount, ItemMoveModifier itemMoveModifier)
    {
        Puts("CanMoveItem works!");
        return null;
    }

##### __**CanStackItem**

  * Called when moving an item onto another item
  * Returning true or false overrides default behavior

     bool CanStackItem(Item item, Item targetItem)
    {
        Puts("CanStackItem works!");
        return true;
    }

##### __**OnFuelConsumed**

  * Called after fuel is used (furnace, lanterns, camp fires, etc.)
  * No return behavior

     void OnFuelConsumed(BaseOven oven, Item fuel, ItemModBurnable burnable)
    {
        Puts("OnFuelConsumed works!");
    }

##### __**OnFuelConsume**

  * Called right before fuel is used (furnace, lanterns, camp fires, etc.)
  * Returning a non-null value overrides default behavior

     object OnFuelConsume(BaseOven oven, Item fuel, ItemModBurnable burnable)
    {
        Puts("OnFuelConsume works!");
        return null;
    }

##### __**OnFindBurnable**

  * Called when looking for fuel for the oven
  * Returning an Item overrides default behavior

     Item OnFindBurnable(BaseOven oven)
    {
        Puts("OnFindBurnable works!");
        return null;
    }

##### __**OnHealingItemUse**

  * Called when a player attempts to use a medical tool
  * Returning a non-null value overrides default behavior

     object OnHealingItemUse(MedicalTool tool, BasePlayer player)
    {
        Puts("OnHealingItemUse works!");
        return null;
    }

##### __**OnItemAction**

  * Called when a button is clicked on an item in the inventory (drop, unwrap, ...)
  * Returning a non-null value overrides default behavior

     object OnItemAction(Item item, string action, BasePlayer player)
    {
        Puts("OnItemAction works!");
        return null;
    }

##### __**OnItemAddedToContainer**

  * Called right after an item was added to a container
  * An entire stack has to be created, not just adding more wood to a wood stack for example
  * No return behavior

     void OnItemAddedToContainer(ItemContainer container, Item item)
    {
        Puts("OnItemAddedToContainer works!");
    }

##### __**OnItemCraft**

  * Called just before an item is added to the crafting queue
  * Returning true or false overrides default behavior

     object OnItemCraft(ItemCraftTask task, BasePlayer player, Item item)
    {
        Puts("OnItemCraft works!");
        return null;
    }

##### __**OnItemCraftCancelled**

  * Called before an item has been crafted
  * No return behavior

     void OnItemCraftCancelled(ItemCraftTask task)
    {
        Puts("OnItemCraftCancelled works!");
    }

##### __**OnItemCraftFinished**

  * Called right after an item has been crafted
  * No return behavior

     void OnItemCraftFinished(ItemCraftTask task, Item item)
    {
        Puts("OnItemCraftFinished works!");
    }

##### __**OnItemDeployed**

  * Called right after an item has been deployed
  * No return behavior

     void OnItemDeployed(Deployer deployer, BaseEntity entity, BaseEntity slotEntity)
    {
        Puts("OnItemDeployed works!");
    }

##### __**OnItemDropped**

  * Called right after an item has been dropped
  * No return behavior

     void OnItemDropped(Item item, BaseEntity entity)
    {
        Puts("OnItemDropped works!");
    }

##### __**OnItemPickup**

  * Called right after an item has been picked up
  * Returning a non-null value overrides default behavior

     object OnItemPickup(Item item, BasePlayer player)
    {
        Puts("OnItemPickup works!");
        return null;
    }

##### __**OnItemRemovedFromContainer**

  * Called right after an item was removed from a container
  * The entire stack has to be removed for this to be called, not just a little bit
  * No return behavior

     void OnItemRemovedFromContainer(ItemContainer container, Item item)
    {
        Puts("OnItemRemovedFromContainer works!");
    }

##### __**OnItemRepair**

  * Called right before an item is repaired
  * Returning a non-null value overrides default behavior

     object OnItemRepair(BasePlayer player, Item item)
    {
        Puts("OnItemRepair works!");
        return null;
    }

##### __**OnItemResearch**

  * Called right before a player begins to research an item
  * No return behavior

     void OnItemResearch(ResearchTable table, Item targetItem, BasePlayer player)
    {
        Puts("OnItemResearch works!");
    }

##### __**OnItemResearched**

  * Called right before a player finishes researching an item
  * Returning a float will affect if researching is successful or not

     float OnItemResearched(ResearchTable table, float chance)
    {
        Puts("OnItemResearched works!");
        return 1;
    }

##### __**OnResearchCostDetermine**

  * Called when an item is being scrapped at a research table or when a blueprint is being unlocked in a tech tree
  * Returning a numeric value (int) overrides the default value

     object OnResearchCostDetermine(Item item, ResearchTable researchTable)
    {
        Puts("OnResearchCostDetermine works!");
        return null;
    }

     object OnResearchCostDetermine(ItemDefinition itemDefinition)
    {
        Puts("OnResearchCostDetermine works!");
        return null;
    }

##### __**OnItemSplit**

  * Called right before an item is split into multiple stacks
  * Returning an Item overrides default behavior

     Item OnItemSplit(Item item, int amount)
    {
        Puts("OnItemSplit works!");
        return null;
    }

##### __**OnItemUpgrade**

  * Called right before an item is upgraded
  * No return behavior

     void OnItemUpgrade(Item item, Item upgraded, BasePlayer player)
    {
        Puts("OnItemUpgrade works!");
    }

##### __**OnItemUse**

  * Called when an item is used
  * Returning an int overrides the amount consumed.

     int OnItemUse(Item item, int amountToUse)
    {
        Puts("OnItemUse works!");
        return amountToUse;
    }

##### __**OnLoseCondition**

  * Called right before the condition of the item is modified
  * No return behavior

     void OnLoseCondition(Item item, ref float amount)
    {
        Puts("OnLoseCondition works!");
    }

##### __**OnMaxStackable**

  * Called when an items max stackable is calculated
  * Returning a numeric value (int) overrides the default value

     int OnMaxStackable(Item item)
    {
        Puts("OnMaxStackable works!");
        return 1;
    }

##### __**OnTrapArm**

  * Called when the player arms a bear trap
  * Returning a non-null value overrides default behavior

     object OnTrapArm(BearTrap trap, BasePlayer player)
    {
        Puts("OnTrapArm works!");
        return null;
    }

##### __**OnTrapDisarm**

  * Called when the player disarms a land mine
  * Returning a non-null value overrides default behavior

     object OnTrapDisarm(Landmine trap, BasePlayer player)
    {
        Puts("OnTrapDisarm works!");
        return null;
    }

##### __**OnTrapSnapped**

  * Called when a trap is triggered by a game object
  * No return behavior

     void OnTrapSnapped(BaseTrapTrigger trap, GameObject go)
    {
        Puts("OnTrapSnapped works!");
    }

##### __**OnTrapTrigger**

  * Called when a trap is triggered by a game object
  * Returning a non-null value overrides default behavior

     object OnTrapTrigger(BaseTrap trap, GameObject go)
    {
        Puts("OnTrapTrigger works!");
        return null;
    }

##### __**OnBonusItemDrop**

  * Called when a loot container is about to drop bonus scrap for a player who has a corresponding tea buff
  * Returning a non-null value overrides default behavior

     object OnBonusItemDrop(Item item, BasePlayer player)
    {
        Puts("OnBonusItemDrop works!");
        return null;
    }

##### __**OnBonusItemDropped**

  * Called after a loot container has dropped bonus scrap for a player who has a corresponding tea buff
  * No return behavior

     void OnBonusItemDropped(Item item, BasePlayer player)
    {
        Puts("OnBonusItemDropped works!");
    }

##### __**OnItemRefill**

  * Called right before an item such as a diving tank is repaired without using a repair bench
  * Returning a non-null value overrides default behavior

     object OnItemRefill(Item item, BasePlayer player)
    {
        Puts("OnItemRefill works!");
        return null;
    }

##### __**OnItemLock**

  * Called right before an item is locked, such as in a modular car inventory
  * Returning a non-null value overrides default behavior

     object OnItemLock(Item item)
    {
        Puts("OnItemLock works!");
        return null;
    }

##### __**OnItemUnlock**

  * Called right before an item is unlocked, such as in a modular car inventory
  * Returning a non-null value overrides default behavior

     object OnItemUnlock(Item item)
    {
        Puts("OnItemUnlock works!");
        return null;
    }

##### __**OnItemSubmit**

  * Called when a player submits an item into a mailbox or dropbox
  * Returning a non-null value cancels default behavior

     object OnItemSubmit(Item item, Mailbox mailbox, BasePlayer player)
    {
        Puts("OnItemSubmit works!");
        return null;
    }

##### __**OnItemStacked**

  * Called after an item has been stacked
  * No return behavior

     void OnItemStacked(Item destinationItem, Item sourceItem, ItemContainer destinationContainer)
    {
        Puts("OnItemStacked works!");
    }

##### __**OnIngredientsCollect**

  * Called when ingredients are about to be collected for crafting an item
  * Returning a non-null value cancels default behavior

     bool? OnIngredientsCollect(ItemCrafter itemCrafter, ItemBlueprint blueprint, ItemCraftTask task, int amount, BasePlayer player)
    {
        Puts("OnIngredientsCollect works!");
        return null;
    }

## __Resource Hooks

#####  __**OnCollectiblePickup**

  * Called when the player collects an item
  * Returning a non-null value overrides default behavior

     object OnCollectiblePickup(CollectibleEntity collectible, BasePlayer player)
    {
        Puts("OnCollectiblePickup works!");
        return null;
    }

##### __**CanTakeCutting**

  * Called when a player is trying to take a cutting (clone) of a GrowableEntity
  * Returning a non-null value cancels default behavior

     object CanTakeCutting(BasePlayer player, GrowableEntity entity)
    {
        Puts("CanTakeCutting works!");
        return null;
    }

##### __**OnQuarryToggled**

  * Called when a quarry has just been toggled
  * No return behavior

     void OnQuarryToggled(MiningQuarry quarry, BasePlayer player)
    {
        Puts($"{player.displayName} has toggled a quarry");
    }

##### __**OnGrowableGathered**

  * Called before the player receives an item from gathering a growable entity
  * No return behavior

     void OnGrowableGathered(GrowableEntity plant, Item item, BasePlayer player)
    {
        Puts($"{player.displayName} has gathered {item.info.shortname} x {item.amount}.");
    }

##### __**OnRemoveDying**

  * Called when a player is trying to harvest a dying growable entity
  * Returning a non-null value overrides default behavior

     object OnRemoveDying(GrowableEntity plant, BasePlayer player)
    {
        Puts("OnRemoveDying works!");
        return null;
    }

##### __**OnGrowableGather**

  * Called when the player gathers a growable entity
  * Returning a non-null value overrides default behavior

     object OnGrowableGather(GrowableEntity plant, BasePlayer player)
    {
        Puts("OnGrowableGather works!");
        return null;
    }

##### __**OnDispenserBonus**

  * Called before the player is given a bonus item for gathering
  * Returning an Item replaces the existing Item

     void OnDispenserBonus(ResourceDispenser dispenser, BasePlayer player, Item item)
    {
        Puts("OnDispenserBonus works!");
    }

##### __**OnDispenserGather**

  * Called before the player is given items from a resource
  * Returning a non-null value overrides default behavior

     object OnDispenserGather(ResourceDispenser dispenser, BasePlayer player, Item item)
    {
        Puts("OnDispenserGather works!");
        return null;
    }

##### __**OnQuarryEnabled**

  * Called when a mining quarry is turned on/enabled
  * No return behavior

     void OnQuarryEnabled(MiningQuarry quarry)
    {
        Puts("OnQuarryEnabled works!");
    }

##### __**OnTreeMarkerHit**

  * Called when a player hits a tree with a tool (rock, hatchet, etc.)
  * Returning true or false overrides default behaviour

     bool? OnTreeMarkerHit(TreeEntity tree, HitInfo info)
    {
        Puts("OnTreeMarkerHit works!");
        return null;
    }

##### __**OnQuarryGather**

  * Called before items are gathered from a quarry
  * No return behavior

     void OnQuarryGather(MiningQuarry quarry, Item item)
    {
        Puts("OnQuarryGather works!");
    }

##### __**OnSurveyGather**

  * Called before items are gathered from a survey charge
  * No return behavior

     void OnSurveyGather(SurveyCharge survey, Item item)
    {
        Puts("OnSurveyGather works!");
    }

## __Structure Hooks

#####  __**OnCodeEntered**

  * Called when the player has entered a code in a codelock
  * Returning a non-null value overrides default behavior

     object OnCodeEntered(CodeLock codeLock, BasePlayer player, string code)
    {
        Puts("OnCodeEntered works!");
        return null;
    }

##### __**OnCupboardAuthorize**

  * Called when a cupboard attempts to authorize a player
  * Returning a non-null value overrides default behavior

     object OnCupboardAuthorize(BuildingPrivlidge privilege, BasePlayer player)
    {
        Puts("OnCupboardAuthorize works!");
        return null;
    }

##### __**OnCupboardClearList**

  * Called when an attempt is made to clear a cupboard authorized list
  * Returning a non-null value overrides default behavior

     object OnCupboardClearList(BuildingPrivlidge privilege, BasePlayer player)
    {
        Puts("OnCupboardClearList works!");
        return null;
    }

##### __**OnCupboardDeauthorize**

  * Called when a cupboard attempts to deauthorize a player
  * Returning a non-null value overrides default behavior

     object OnCupboardDeauthorize(BuildingPrivlidge privilege, BasePlayer player)
    {
        Puts("OnCupboardDeauthorize works!");
        return null;
    }

##### __**OnDoorClosed**

  * Called when the player closed a door
  * No return behavior

     void OnDoorClosed(Door door, BasePlayer player)
    {
        Puts("OnDoorClosed works!");
    }

##### __**OnDoorOpened**

  * Called when the player opened a door
  * No return behavior

     void OnDoorOpened(Door door, BasePlayer player)
    {
        Puts("OnDoorOpened works!");
    }

##### __**OnDoorKnocked**

  * Called when the player knocks on a door
  * No return behavior

     void OnDoorKnocked(Door door, BasePlayer player)
    {
        Puts("OnDoorKnocked works!");
    }

##### __**OnEntityBuilt**

  * Called when any structure is built (walls, ceilings, stairs, etc.)
  * No return behavior

     void OnEntityBuilt(Planner plan, GameObject go)
    {
        Puts("OnEntityBuilt works!");
    }

##### __**OnHammerHit**

  * Called when the player has hit something with a hammer
  * Returning a non-null value overrides default behavior

     object OnHammerHit(BasePlayer player, HitInfo info)
    {
        Puts("OnHammerHit works!");
        return null;
    }

##### __**OnStructureDemolish**

  * Called when the player selects Demolish or DemolishImmediate from the BuildingBlock or BaseCombatEntity menu
  * Returning a non-null value overrides default behavior

     object OnStructureDemolish(BaseCombatEntity entity, BasePlayer player, bool immediate)
    {
        Puts("OnStructureDemolish works!");
        return null;
    }

##### __**OnStructureRepair**

  * Called when the player repairs a BuildingBlock or BaseCombatEntity
  * Returning a non-null value cancels repair

     object OnStructureRepair(BaseCombatEntity entity, BasePlayer player)
    {
        Puts("OnStructureRepair works!");
        return null;
    }

##### __**OnStructureRotate**

  * Called when the player rotates a BuildingBlock or BaseCombatEntity
  * Returning a non-null value cancels rotate

     object OnStructureRotate(BaseCombatEntity entity, BasePlayer player)
    {
        Puts("OnStructureRotate works!");
        return null;
    }

##### __**OnStructureUpgrade**

  * Called when the player upgrades the grade of a BuildingBlock or BaseCombatEntity
  * Returning a non-null value overrides default behavior

     object OnStructureUpgrade(BaseCombatEntity entity, BasePlayer player, BuildingGrade.Enum grade)
    {
        Puts("OnStructureUpgrade works!");
        return null;
    }

##### __**OnConstructionPlace**

  * Called when a player tries to place a building block
  * Returning a non-null value overrides default behavior

     object OnConstructionPlace(BaseEntity entity, Construction component, Construction.Target constructionTarget, BasePlayer player)
    {
        Puts("OnConstructionPlace works!");
        return null;
    }

##### __**OnBuildingSplit**

  * Called when a building is split into two
  * No return behavior

     void OnBuildingSplit(BuildingManager.Building building, uint newBuildingId)
    {
        Puts("OnBuildingSplit works!");
    }

## __Terrain Hooks

#####  __**OnTerrainInitialized**

  * Called after the terrain generation process has completed
  * No return behavior

     void OnTerrainInitialized()
    {
        Puts("OnTerrainInitialized works!");
    }

## __Vending Hooks

#####  __**CanAdministerVending**

  * Called when a player attempts to administer a vending machine
  * Returning true or false overrides default behavior

     bool CanAdministerVending(BasePlayer player, VendingMachine machine)
    {
        Puts("CanAdministerVending works!");
        return true;
    }

##### __**OnTakeCurrencyItem**

  * Called before currency item is taken
  * Returning a non-null value cancels default behavior

     object OnTakeCurrencyItem(VendingMachine vending, Item item)
    {
        Puts("OnTakeCurrencyItem works!");
        return null;
    }

##### __**OnGiveSoldItem**

  * Called before a sold item is given
  * Returning a non-null value cancels default behavior

     object OnGiveSoldItem(NPCVendingMachine vending, Item soldItem, BasePlayer buyer)
    {
        Puts("OnGiveSoldItem works!");
        return null;
    }

     object OnGiveSoldItem(VendingMachine vending, Item soldItem, BasePlayer buyer)
    {
        Puts("OnGiveSoldItem works!");
        return null;
    }

##### __**OnVendingShopRename**

  * Called when a player tries to rename vending shop
  * Returning a non-null value cancels default behavior

     object OnVendingShopRename(VendingMachine vending, string newName, BasePlayer player)
    {
        Puts("OnVendingShopRename works!");
        return null;
    }

##### __**CanUseVending**

  * Called when a player attempts to use a vending machine
  * Returning true or false overrides default behavior

     bool CanUseVending(BasePlayer player, VendingMachine machine)
    {
        Puts("CanUseVending works!");
        return true;
    }

##### __**CanVendingAcceptItem**

  * Called when a player attempts to administer a vending machine
  * Returning true or false overrides default behavior

     bool CanVendingAcceptItem(VendingMachine vending, Item item, int targetPos)
    {
        Puts("CanVendingAcceptItem works!");
        return true;
    }

##### __**OnAddVendingOffer**

  * Called when a sell order/offer is added to a vending machine
  * No return behavior

     void OnAddVendingOffer(VendingMachine machine, ProtoBuf.VendingMachine.SellOrder sellOrder)
    {
        Puts("OnAddVendingOffer works!");
    }

##### __**OnBuyVendingItem**

  * Called when a player buys an item from a vending machine
  * Returning a non-null value overrides default behavior

     object OnBuyVendingItem(VendingMachine machine, BasePlayer player, int sellOrderId, int numberOfTransactions)
    {
        Puts("OnBuyVendingItem works!");
        return null;
    }

##### __**OnDeleteVendingOffer**

  * Called when a sell order/offer is deleted from a vending machine
  * No return behavior

     void OnDeleteVendingOffer(VendingMachine machine, int offerId)
    {
        Puts("OnDeleteVendingOffer works!");
    }

##### __**OnOpenVendingAdmin**

  * Called when a player opens the admin ui for a vending machine
  * No return behavior

     void OnOpenVendingAdmin(VendingMachine machine, BasePlayer player)
    {
        Puts("OnOpenVendingAdmin works!");
    }

##### __**OnVendingShopOpened**

  * Called when a player opens the customer ui for a vending machine
  * No return behavior

     void OnVendingShopOpened(VendingMachine machine, BasePlayer player)
    {
        Puts("OnVendingShopOpened works!");
    }

##### __**OnRefreshVendingStock**

  * Called when the stock on a vending machine is updated
  * No return behavior

     void OnRefreshVendingStock(VendingMachine machine, Item item)
    {
        Puts("OnRefreshVendingStock works!");
    }

##### __**OnRotateVendingMachine**

  * Called when a player attempts to rotate a vending machine
  * Returning a non-null value overrides default behavior

     object OnRotateVendingMachine(VendingMachine machine, BasePlayer player)
    {
        Puts("OnRotateVendingMachine works!");
        return null;
    }

##### __**OnToggleVendingBroadcast**

  * Called when a player toggles the broadcasting of the vending machine
  * No return behavior

     void OnToggleVendingBroadcast(VendingMachine machine, BasePlayer player)
    {
        Puts("OnToggleVendingBroadcast works!");
    }

##### __**OnVendingTransaction**

  * Called when a player attempts to buy an item from a vending machine
  * Returning true or false overrides default behavior

     bool OnVendingTransaction(VendingMachine machine, BasePlayer buyer, int sellOrderId, int numberOfTransactions)
    {
        Puts("OnVendingTransaction works!");
        return true;
    }

##### __**OnNpcGiveSoldItem**

  * Called before a non-player controlled vending machine (at outpost etc.) gives the player the item they purchased.
  * Returning a non-null value overrides default behaviour.

     object OnNpcGiveSoldItem(NPCVendingMachine machine, Item soldItem, BasePlayer buyer)
    {
        Puts("OnNpcGiveSoldItem works!");
        return null;
    }

## __Weapon Hooks

#####  __**CanCreateWorldProjectile**

  * Called when the item creates a projectile in the world
  * Returning a non-null value overrides default behavior

     object CanCreateWorldProjectile(HitInfo info, ItemDefinition itemDef)
    {
        Puts("CanCreateWorldProjectile works!");
        return null;
    }

##### __**OnProjectileRicochet**

  * Called when a player's weapon projectile ricochets
  * Returning a non-null value overrides default behavior

     object OnProjectileRicochet(BasePlayer player, PlayerProjectileRicochet ricochet)
    {
        Puts("OnProjectileRicochet works!");
        return null;
    }

##### __**OnExplosiveDud**

  * Called when explosive tries to become dud
  * Returning a non-null value cancels default behavior

     object OnExplosiveDud(DudTimedExplosive explosive)
    {
        Puts("OnExplosiveDud works!");
        return null;
    }

##### __**OnAmmoUnload**

  * Called when a player is trying to unload ammo
  * Returning a non-null value cancels default behavior

     object OnAmmoUnload(BaseProjectile projectile, Item item, BasePlayer player)
    {
        Puts("OnAmmoUnload works!");
        return null;
    }

##### __**OnExplosiveFuseSet**

  * Called when a fuse of an explosive is set
  * Returning a non-null value overwrites fuse length

     object OnExplosiveFuseSet(TimedExplosive explosive, float fuseLength)
    {
        Puts("OnExplosiveFuseSet works!");
        return null;
    }

##### __**CanExplosiveStick**

  * Called when a Timed Explosive is attempting to stick to another entity
  * Returning a non-null value overwrites the default behavior

     object CanExplosiveStick(TimedExplosive explosive, BaseEntity entity)
    {
        Puts("CanExplosiveStick works!");
        return null;
    }

##### __**OnWorldProjectileCreate**

  * Called when a projectile is created
  * Returning a non-null value overrides default behavior

     object OnWorldProjectileCreate(HitInfo hitInfo, Item item)
    {
        Puts("OnWorldProjectileCreate works!");
        return null;
    }

##### __**OnExplosiveDropped**

  * Called when the player drops an explosive item (C4, grenade, ...)
  * No return behavior

     void OnExplosiveDropped(BasePlayer player, BaseEntity entity, ThrownWeapon item)
    {
        Puts("OnExplosiveDropped works!");
    }

##### __**OnExplosiveThrown**

  * Called when the player throws an explosive item (C4, grenade, ...)
  * No return behavior

     void OnExplosiveThrown(BasePlayer player, BaseEntity entity, ThrownWeapon item)
    {
        Puts("OnExplosiveThrown works!");
    }

##### __**OnFlameThrowerBurn**

  * Called when the burn from a flame thrower spreads
  * No return behavior

     void OnFlameThrowerBurn(FlameThrower thrower, BaseEntity flame)
    {
        Puts("OnFlameThrowerBurn works!");
    }

##### __**OnMeleeThrown**

  * Called when the player throws a melee item (axe, rock, ...)
  * No return behavior

     void OnMeleeThrown(BasePlayer player, Item item)
    {
        Puts("OnMeleeThrown works!");
    }

##### __**OnMagazineReload**

  * Called when the player reloads a magazine
  * Returning a non-null value overrides default behavior

     object OnMagazineReload(BaseProjectile weapon, IAmmoContainer desiredAmount, BasePlayer player)
    {
        Puts("OnMagazineReload works!");
        return null;
    }

##### __**OnWeaponReload**

  * Called when the player reloads a weapon
  * Returning a non-null value overrides default behavior

     object OnWeaponReload(BaseProjectile weapon, BasePlayer player)
    {
        Puts("OnWeaponReload works!");
        return null;
    }

##### __**OnRocketLaunched**

  * Called when the player launches a rocket
  * No return behavior

     void OnRocketLaunched(BasePlayer player, BaseEntity entity)
    {
        Puts("OnRocketLaunched works!");
    }

##### __**OnAmmoSwitch**

  * Called when the player starts to switch the ammo in a weapon
  * Returning a non-null value overrides default behavior

     object OnAmmoSwitch(BaseProjectile weapon, BasePlayer player)
    {
        Puts("OnAmmoSwitch works!");
        return null;
    }

##### __**OnWeaponFired**

  * Called when the player fires a weapon
  * No return behavior

     void OnWeaponFired(BaseProjectile projectile, BasePlayer player, ItemModProjectile mod, ProtoBuf.ProjectileShoot projectiles)
    {
        Puts("OnWeaponFired works!");
    }

## __Vehicle Hooks

#####  __**CanUseHelicopter**

  * Useful for denying to mount a CH47 helicopter
  * Returning a non-null value cancels default behavior

     object CanUseHelicopter(BasePlayer player, CH47HelicopterAIController helicopter)
    {
        Puts("CanUseHelicopter works!");
        return null;
    }

##### __**OnBoatPathGenerate**

  * Called when generating ocean patrol path for CargoShip
  * Returning a List<Vector3> overrides default behavior

    List<Vector3> OnBoatPathGenerate()
    {
        Puts("OnBoatPathGenerate works!");
        return null;
    }

##### __**OnVehicleModuleMove**

  * Called when a player tries to move a vehicle module item that is currently on a vehicle
  * Returning a non-null value overrides default behavior

     object OnVehicleModuleMove(BaseVehicleModule module, BaseModularVehicle vehicle, BasePlayer player)
    {
        Puts("OnVehicleModuleMove works!");
        return null;
    }

##### __**OnEngineStart**

  * Called when a player tries to start a vehicle engine
  * Returning a non-null value overrides default behavior

     object OnEngineStart(BaseVehicle vehicle, BasePlayer driver)
    {
        Puts("OnEngineStart works!");
        return null;
    }

##### __**OnEngineStarted**

  * Called right after a vehicle engine has started
  * No return behavior

     void OnEngineStarted(BaseVehicle vehicle, BasePlayer driver)
    {
        Puts("OnEngineStarted works!");
    }

##### __**OnEngineStopped**

  * Called right after a vehicle engine has stopped
  * No return behavior

     void OnEngineStopped(BaseVehicle vehicle)
    {
        Puts("OnEngineStopped works!");
    }

##### __**OnEngineStop**

  * Called when a vehicle engine is about to stop
  * Returning a non-null value overrides default behavior

     object OnEngineStop(BaseVehicle vehicle)
    {
        Puts("OnEngineStop works!");
        return null;
    }

##### __**OnEngineStatsRefresh**

  * Called right before the stats of a modular car engine are refreshed
  * Returning a non-null value overrides default behavior

     object OnEngineStatsRefresh(VehicleModuleEngine engineModule, EngineStorage engineStorage)
    {
        Puts("OnEngineStatsRefresh works!");
        return null;
    }

##### __**OnEngineStatsRefreshed**

  * Called right after the stats of a modular car engine are refreshed
  * No return behavior

     void OnEngineStatsRefreshed(VehicleModuleEngine engineModule, EngineStorage engineStorage)
    {
        Puts("OnEngineStatsRefreshed works!");
    }

##### __**OnVehicleModuleSelect**

  * Called right after a player has selected a vehicle module item in a car inventory, but before they are shown the corresponding storage container
  * Returning a non-null value overrides default behavior

     object OnVehicleModuleSelect(Item moduleItem, ModularCarGarage carLift, BasePlayer player)
    {
        Puts("OnVehicleModuleSelect works!");
        return null;
    }

##### __**OnVehicleModuleSelected**

  * Called right after a player has selected a vehicle module item in a car's inventory, and after they have been shown the corresponding storage container if applicable
  * No return behavior

     void OnVehicleModuleSelected(Item moduleItem, ModularCarGarage carLift, BasePlayer player)
    {
        Puts("OnVehicleModuleSelected works!");
    }

##### __**OnVehicleModuleDeselected**

  * Called right after a player deselects a vehicle module item in a car's inventory
  * No return behavior

     void OnVehicleModuleDeselected(ModularCarGarage carLift, BasePlayer player)
    {
        Puts("OnVehicleModuleDeselected works!");
    }

##### __**OnHotAirBalloonToggle**

  * Called when a player tries to toggle a hot air balloon on or off
  * Returning a non-null value overrides default behavior

     object OnHotAirBalloonToggle(HotAirBalloon balloon, BasePlayer player)
    {
        Puts("OnHotAirBalloonToggle works!");
        return null;
    }

##### __**OnHotAirBalloonToggled**

  * Called right after a player has toggled a hot air balloon on or off
  * No return behavior

     void OnHotAirBalloonToggled(HotAirBalloon balloon, BasePlayer player)
    {
        Puts("OnHotAirBalloonToggled works!");
    }

##### __**OnVehicleModulesAssign**

  * Called right after a modular car has spawned, but before module items are added to its inventory from a preset
  * Returning a non-null value overrides default behavior

     object OnVehicleModulesAssign(ModularCar car, ItemModVehicleModule[] modulePreset)
    {
        Puts("OnVehicleModulesAssign works!");
        return null;
    }

##### __**OnVehicleModulesAssigned**

  * Called right after a car has spawned and its module inventory has been filled with module items from a preset
  * No return behavior

     void OnVehicleModulesAssigned(ModularCar car, ItemModVehicleModule[] modulePreset)
    {
        Puts("OnVehicleModulesAssigned works!");
    }

##### __**OnVehiclePush**

  * Called when a player tries to push a vehicle
  * Returning a non-null value overrides default behavior

     object OnVehiclePush(BaseVehicle vehicle, BasePlayer player)
    {
        Puts("OnVehiclePush works!");
        return null;
    }

##### __**CanCheckFuel**

  * Called when a player tries to loot a vehicle's fuel container
  * Returning true or false overrides default behavior

     object CanCheckFuel(EntityFuelSystem fuelSystem, StorageContainer fuelContainer, BasePlayer player)
    {
        Puts("CanCheckFuel works!");
        return null;
    }

##### __**CanUseFuel**

  * Called before a vehicle fuel system consumes fuel
  * Returning true or false overrides default behavior

     object CanUseFuel(EntityFuelSystem fuelSystem, StorageContainer fuelContainer, float currentSeconds, float fuelPerSecond)
    {
        Puts("CanUseFuel works!");
        return null;
    }

##### __**OnFuelCheck**

  * Called when determining whether a vehicle has sufficient fuel
  * Returning true or false overrides default behavior

     object OnFuelCheck(EntityFuelSystem fuelSystem)
    {
        Puts("OnFuelCheck works!");
        return null;
    }

##### __**OnFuelAmountCheck**

  * Called when the amount of fuel in a vehicle is being determined
  * Returning a numeric value (int) overrides the default value

     object OnFuelAmountCheck(EntityFuelSystem fuelSystem, Item fuelItem)
    {
        Puts("OnFuelAmountCheck works!");
        return null;
    }

##### __**OnFuelItemCheck**

  * Called when determining which item should be used to fuel a vehicle
  * Returning an Item overrides default behavior

     object OnFuelItemCheck(EntityFuelSystem fuelSystem, StorageContainer fuelContainer)
    {
        Puts("OnFuelItemCheck works!");
        return null;
    }

## __Team Hooks

##  __World Hooks

##  __Fishing Hooks

##  __Electronic Hooks

##  __Clan Hooks

##  __Sign Hooks

##  __TechTree Hooks

##  __Phone Hooks

##  __Plugin Hooks

##  __Permission Hooks
