# uMod - Continuous Integration

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * GitHub app
    * Documentation
    * Licensing
    * Releases
    * Branches
    * Issues
  * GitLab app
  * Builds

# Continuous Integration

For experienced developers, uMod seamlessly integrates both ways with GitHub and GitLab (Coming Soon).

* * *

## GitHub app

By installing the GitHub app on a repository, the entire plugin development life-cycle may be managed solely through GitHub.

### Documentation

Adding or updating a README.md file in a plugin repository will automatically update the documentation displayed for that plugin on uMod.org

### Licensing

Adding or updating a LICENSE.md file in a plugin repository will automatically update the license displayed for that plugin on uMod.org

### Releases

Adding a release of your plugin on GitHub will automatically push a public update of your plugin on uMod.org.

### Branches

 _Coming Soon!_

### Issues

 _Coming Soon!_

## GitLab app

 _Coming Soon!_

## Builds

Our CI server will compile and inspect the plugin automatically against (nearly) all games that the plugin supports upon release.

If a build fails, the author will be notified and the release may be hidden from view.
