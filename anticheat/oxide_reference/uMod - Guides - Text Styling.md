# uMod - Guides - Text Styling

2 minutes to read

Created by

<PERSON><PERSON>

Updated by

<PERSON><PERSON>

#  Text Styling

####  Details on how to style text

This guide is for uMod,_**not** Oxide_.

  * Formatting
    * Bold
    * Italic
    * Color
    * RGB
    * RGBA
    * Size
  * Player messages

uMod features a custom markup language, which is used for creating universally styling text across games. It works by converting any text passed through the supported methods and converts it to the compatible text styling for each specific game (if the game supports text styling).

## Formatting

### Bold

`[b]bold text[/b]`

     string text = "[b]hello world[/b]";
    string formattedText = uMod.Text.Formatter.Format(text);
    Logger.Info(formattedText);

### Italic

`[i]italic text[/i]`

     string text = "[i]hello world[/i]";
    string formattedText = uMod.Text.Formatter.Format(text);
    Logger.Info(formattedText);

### Color

`[#white]white text[/#]`
`[#black]black text[/#]`

**Color Names**

  * aqua
  * black
  * blue
  * brown
  * cyan
  * darkblue
  * fuchsia
  * green
  * grey
  * lightblue
  * lime
  * magenta
  * maroon
  * navy
  * olive
  * orange
  * purple
  * red
  * silver
  * teal
  * white
  * yellow

#### RGB

`[#FFFFFF]white text[/#]`
`[#000000]black text[/#]`

     string text = "[#342543]hello world[/#]";
    string formattedText = uMod.Text.Formatter.Format(text);
    Logger.Info(formattedText);

#### RGBA

`[#FFFFFFFF]white text[/#]`
`[#00000FF]black text[/#]`

     string text = "[#34254350]hello world[/#]";
    string formattedText = uMod.Text.Formatter.Format(text);
    Logger.Info(formattedText);

### Size

`[+4]size 4 text[/4]`

     string text = "[+4]hello world[/4]";
    string formattedText = uMod.Text.Formatter.Format(text);
    Logger.Info(formattedText);

## Player messages

Sending messages to players using `IPlayer.Message` or `IPlayer.Reply` will automatically format messages.

Logging

### The Basics

  1. Plugins
  2. Hooks
  3. Commands
  4. Validation
  5. Configuration
  6. Localization
  7. Schematics
  8. Filesystem
  9. Preprocessors
  10. Gates
  11. Players
  12. Integration
  13. Timers
  14. Logging
  15. **Text Styling**
