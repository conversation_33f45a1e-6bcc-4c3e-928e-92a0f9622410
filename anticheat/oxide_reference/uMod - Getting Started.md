# uMod - Getting Started

#### __API

##### Basics

OverviewGetting StartedStyle GuideConfigurationData FilesLocalizationHooksPlayerSecurityDependenciesIntegrationContinuous IntegrationPreprocessor DirectivesApproval Guide

##### Libraries

TimersWeb RequestsCommandsPermissionsDatabase

  * Namespace
  * Title
  * Author
  * Version
  * Description
  * Class Name
  * Plugin Type

# Getting Started

* * *

Below is a minimal example of an Oxide plugin.

     namespace Oxide.Plugins
    {
        [Info("Epic Stuff", "Unknown Author", "0.1.0")]
        [Description("Makes epic stuff happen")]
        class EpicStuff : CovalencePlugin
        {
            private void Init()
            {
                Puts("A baby plugin is born!");
            }

            // The rest of the code magic

            // TODO (you): Make more epic stuff
        }
    }

## Namespace

The namespace of each plugin should be `Oxide.Plugins` and will always be the same due to how plugins are currently setup to interact with each other and the core.

     namespace Oxide.Plugins

## Title

The Title variable _(first part of Info attribute)_ is what defines the plugin. This should be a unique name or codename related to the purpose of the plugin. This variable (Title) can be accessed throughout the plugin in non-static methods as well.

     "Epic Stuff"

## Author

The Author variable _(second part of Info attribute)_ is used to show who made or currently maintains the plugin. This should match the author's uMod.org username (if releasing). This (Author) variable can be accessed throughout the plugin in non-static methods as well.

     "Unknown Author"

## Version

The Version variable _(third part of Info attribute)_ is used to tell if the plugin is outdated or not and should be incremented with each release. Semantic Versioning is recommended. This variable (Version) can be accessed throughout the plugin in non-static methods as well.

     "0.1.0"

## Description

The optional Description variable _(standalone Description attribute)_ helps explain to users what the plugin does, in case the title is not enough. Make it good, but not too long! This (Description) variable can be accessed throughout the plugin in non-static methods as well.

    [Description("Makes epic stuff happen")]

## Class Name

The class name of the plugin is also the Name variable. This needs to match the filename, otherwise warnings will be issued. The class name should not contain any spaces or numbers, and should always start with a capital letter. This variable (Name) can be accessed throughout the plugin in non-static methods as well.

     class EpicStuff

## Plugin Type

Plugins should be inheriting the **CovalencePlugin** type. The CovalencePlugin type is used for both universal and non-universal plugins.

     class EpicStuff : CovalencePlugin
