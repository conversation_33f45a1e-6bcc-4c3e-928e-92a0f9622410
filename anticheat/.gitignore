# AntiCheat Plugin .gitignore

# Compiled binaries
*.dll
*.exe
*.pdb
*.cache

# Visual Studio / VS Code
.vs/
.vscode/
*.suo
*.user
*.userosscache
*.sln.docstates
*.vcxproj.filters
*.vcxproj.user

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# MSBuild Binary and Structured Log
*.binlog

# NuGet Packages
*.nupkg
*.snupkg
.nuget/
packages/

# Oxide/uMod specific
*.oxide
data/
logs/
config/
lang/

# Plugin data files
*.json
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
*.log
*.bak
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE files
*.sublime-project
*.sublime-workspace
.idea/
*.iml
*.ipr
*.iws

# Rust server files (if testing locally)
RustDedicated_Data/
server/
saves/
identity/

# Test files
test/
tests/
*.test.cs

# Documentation build
docs/_build/
site/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Backup files
*.backup
*.orig

# Plugin specific data
violations_data.json
player_data.json
anticheat_config.json
anticheat_violations_*.json

# Discord webhook URLs (security)
webhooks.txt
discord_config.json

# Development notes
TODO.md
NOTES.md
dev_notes/

# Compiled plugin versions
AntiCheat_*.cs
AntiCheat_v*.cs

# Performance logs
performance.log
benchmark_*.txt

# Debug output
debug_output/
violation_logs/
trajectory_data/

# Archive files
*.zip
*.rar
*.7z
*.tar.gz

# Certificate files
*.pfx
*.p12
*.key
*.crt
*.pem
