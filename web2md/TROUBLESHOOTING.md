# Web2MD Troubleshooting Guide

## Common Issues and Solutions

### Issue: "Access forbidden (403)" or "Just a moment..." pages

**Problem:** The website you're trying to convert (like umod.org) uses anti-bot protection that blocks automated access.

**Solution:**
1. **Use Manual Save + File Upload (Recommended)**:
   - Visit https://umod.org/documentation/plugins/getting-started in your browser
   - Press `Ctrl+S` (Windows/Linux) or `Cmd+S` (Mac)
   - Choose "Webpage, Complete" or "HTML Only"
   - Save the file to your computer
   - In Web2MD, switch to the "File Upload" tab
   - Drag and drop or browse for the saved HTML file

2. **Alternative**: Try different proxy services by refreshing and trying again (the app cycles through multiple proxies)

### Issue: File upload not working

**Problem:** The file upload feature isn't responding.

**Solution:**
- Make sure you're uploading an `.html` or `.htm` file
- Check that the file is smaller than 10MB
- Try refreshing the page and uploading again
- Make sure JavaScript is enabled in your browser

### Issue: file:// URLs not working

**Problem:** Entering `file:///path/to/file.html` doesn't work.

**Solution:**
- This is a browser security limitation
- Use the "File Upload" tab instead - it's more reliable and secure
- The file upload method works with all browsers and doesn't have security restrictions

### Issue: Conversion produces poor quality markdown

**Problem:** The converted markdown is messy or missing content.

**Solution:**
- Try saving the webpage as "Complete Webpage" instead of "HTML Only"
- Some websites have better mobile versions that convert more cleanly
- The app automatically extracts main content, but complex layouts may need manual cleanup

### Issue: Large files taking too long

**Problem:** Very large HTML files (>5MB) are slow to process.

**Solution:**
- The app processes everything locally for privacy, so large files take time
- Consider saving just the article content instead of the complete webpage
- Break large documents into smaller sections

## Tips for Best Results

### For umod.org specifically:
1. Visit the page in your browser
2. Save as "Webpage, Complete" 
3. Upload the saved file using the File Upload tab
4. The app will extract the documentation content and convert it to clean markdown

### For other protected sites:
- Look for "Save as PDF" options and convert PDF to HTML first
- Check if the site has an RSS feed or API
- Some sites have mobile versions with fewer restrictions

### General tips:
- The File Upload method always works and is more private
- Save pages as "Complete Webpage" for better formatting
- Use the download feature to save your converted markdown
- The app works offline once loaded

## Browser Compatibility

**Fully Supported:**
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

**Limited Support:**
- Internet Explorer (not recommended)
- Very old browser versions may have file upload issues

## Privacy and Security

- All processing happens in your browser
- No data is sent to external servers (except for URL fetching via CORS proxies)
- File uploads never leave your computer
- Safe to use with sensitive documents

## Getting Help

If you're still having issues:
1. Check the browser console for error messages (F12 → Console)
2. Try a different browser
3. Make sure you have a stable internet connection for URL fetching
4. For local files, always use the File Upload tab instead of file:// URLs
