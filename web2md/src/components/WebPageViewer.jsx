import { useState, useEffect } from 'react';

const WebPageViewer = ({ url, html, title }) => {
  const [iframeError, setIframeError] = useState(false);
  const [showRawHtml, setShowRawHtml] = useState(false);

  useEffect(() => {
    setIframeError(false);
    setShowRawHtml(false);
  }, [url]);

  const handleIframeError = () => {
    setIframeError(true);
  };

  const createIframeContent = () => {
    if (!html) return '';
    
    // Create a complete HTML document for the iframe
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${title || 'Web Page Preview'}</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              line-height: 1.6;
              margin: 20px;
              color: #333;
            }
            img {
              max-width: 100%;
              height: auto;
            }
            pre {
              background: #f5f5f5;
              padding: 10px;
              border-radius: 4px;
              overflow-x: auto;
            }
            code {
              background: #f5f5f5;
              padding: 2px 4px;
              border-radius: 2px;
            }
            blockquote {
              border-left: 4px solid #ddd;
              margin: 0;
              padding-left: 20px;
              color: #666;
            }
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 10px 0;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f5f5f5;
            }
          </style>
        </head>
        <body>
          ${html}
        </body>
      </html>
    `;
  };

  if (!url && !html) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50 rounded-lg">
        <div className="text-center text-gray-500">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-lg flex items-center justify-center">
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
            </svg>
          </div>
          <p className="text-lg font-medium">Web Page Preview</p>
          <p className="text-sm">Enter a URL to see the web page here</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white rounded-lg shadow-sm border">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-medium text-gray-900 truncate">
            {title || 'Web Page Preview'}
          </h3>
          {url && (
            <p className="text-sm text-gray-500 truncate">{url}</p>
          )}
        </div>
        <div className="flex items-center gap-2 ml-4">
          <button
            onClick={() => setShowRawHtml(!showRawHtml)}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              showRawHtml 
                ? 'bg-blue-100 text-blue-700' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {showRawHtml ? 'Preview' : 'HTML'}
          </button>
          {url && (
            <a
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors"
            >
              Open Original
            </a>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {showRawHtml ? (
          <div className="h-full overflow-auto p-4">
            <pre className="text-xs text-gray-700 whitespace-pre-wrap break-words">
              {html}
            </pre>
          </div>
        ) : (
          <>
            {iframeError ? (
              <div className="h-full flex items-center justify-center p-8">
                <div className="text-center text-gray-500">
                  <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-lg flex items-center justify-center">
                    <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <p className="text-lg font-medium">Preview Not Available</p>
                  <p className="text-sm">The page cannot be displayed in preview mode</p>
                  <button
                    onClick={() => setShowRawHtml(true)}
                    className="mt-3 px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    View HTML Source
                  </button>
                </div>
              </div>
            ) : (
              <iframe
                srcDoc={createIframeContent()}
                className="w-full h-full border-0"
                sandbox="allow-same-origin"
                onError={handleIframeError}
                title="Web Page Preview"
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default WebPageViewer;
