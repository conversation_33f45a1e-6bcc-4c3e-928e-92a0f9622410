import { useState } from 'react';

const ManualSaveGuide = ({ url }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getBrowserInstructions = () => {
    const userAgent = navigator.userAgent;
    
    if (userAgent.includes('Chrome')) {
      return {
        browser: 'Chrome',
        steps: [
          'Right-click on the webpage and select "Save as..."',
          'Choose "Webpage, Complete" or "HTML Only"',
          'Save the file to your computer',
          'Use the "File Upload" tab above to upload the saved HTML file'
        ]
      };
    } else if (userAgent.includes('Firefox')) {
      return {
        browser: 'Firefox',
        steps: [
          'Press Ctrl+S (or Cmd+S on Mac)',
          'Choose "Web Page, complete" or "Web Page, HTML only"',
          'Save the file to your computer',
          'Use the "File Upload" tab above to upload the saved HTML file'
        ]
      };
    } else if (userAgent.includes('Safari')) {
      return {
        browser: 'Safari',
        steps: [
          'Press Cmd+S (or File → Save As...)',
          'Choose "Web Archive" or "Page Source"',
          'Save the file to your computer',
          'Use the "File Upload" tab above to upload the saved HTML file'
        ]
      };
    } else if (userAgent.includes('Edge')) {
      return {
        browser: 'Edge',
        steps: [
          'Press Ctrl+S (or click the three dots menu → Save page as)',
          'Choose "Webpage, Complete" or "Webpage, HTML only"',
          'Save the file to your computer',
          'Use the "File Upload" tab above to upload the saved HTML file'
        ]
      };
    } else {
      return {
        browser: 'Your Browser',
        steps: [
          'Use your browser\'s "Save Page" or "Save As" feature (usually Ctrl+S)',
          'Choose to save as HTML or complete webpage',
          'Save the file to your computer',
          'Use the "File Upload" tab above to upload the saved HTML file'
        ]
      };
    }
  };

  const instructions = getBrowserInstructions();

  return (
    <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div className="flex items-start justify-between">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 className="text-sm font-medium text-yellow-800">
              Can't access this website automatically?
            </h4>
            <p className="text-sm text-yellow-700 mt-1">
              Some websites block automated access. You can manually save the page instead.
            </p>
          </div>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="ml-4 text-yellow-600 hover:text-yellow-800 transition-colors"
        >
          <svg 
            className={`w-5 h-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      {isExpanded && (
        <div className="mt-4 pt-4 border-t border-yellow-200">
          <h5 className="text-sm font-medium text-yellow-800 mb-3">
            How to manually save the page in {instructions.browser}:
          </h5>
          <ol className="text-sm text-yellow-700 space-y-2">
            {instructions.steps.map((step, index) => (
              <li key={index} className="flex items-start">
                <span className="inline-flex items-center justify-center w-5 h-5 bg-yellow-200 text-yellow-800 rounded-full text-xs font-medium mr-3 flex-shrink-0 mt-0.5">
                  {index + 1}
                </span>
                {step}
              </li>
            ))}
          </ol>

          {url && (
            <div className="mt-4 p-3 bg-yellow-100 rounded border">
              <p className="text-xs text-yellow-800 mb-2">
                <strong>Target URL:</strong>
              </p>
              <a 
                href={url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-xs text-blue-600 hover:text-blue-800 break-all"
              >
                {url}
              </a>
            </div>
          )}

          <div className="mt-4 flex items-center text-xs text-yellow-600">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            This method works for any website, even those with strong protection
          </div>
        </div>
      )}
    </div>
  );
};

export default ManualSaveGuide;
