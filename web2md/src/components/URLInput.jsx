import { useState } from 'react';
import FileUpload from './FileUpload';

const URLInput = ({ onUrlSubmit, onFileUpload, isLoading }) => {
  const [url, setUrl] = useState('');
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('url'); // 'url' or 'file'

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    if (!url.trim()) {
      setError('Please enter a URL');
      return;
    }

    // Enhanced URL validation to support file:// URLs
    const webUrlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    const fileUrlPattern = /^file:\/\/\/.+/;
    const trimmedUrl = url.trim();

    // Check if it's a file:// URL
    if (trimmedUrl.startsWith('file://')) {
      if (!fileUrlPattern.test(trimmedUrl)) {
        setError('Please enter a valid file:// URL (e.g., file:///path/to/file.html)');
        return;
      }
    } else {
      // Check web URL
      const testUrl = trimmedUrl.startsWith('http') ? trimmedUrl : `https://${trimmedUrl}`;
      if (!webUrlPattern.test(testUrl)) {
        setError('Please enter a valid URL');
        return;
      }
    }

    onUrlSubmit(trimmedUrl);
  };

  const handleInputChange = (e) => {
    setUrl(e.target.value);
    if (error) setError(''); // Clear error when user starts typing
  };

  const handleFileUpload = (file) => {
    setError('');
    onFileUpload(file);
  };

  const exampleUrls = [
    'https://example.com',
    'https://github.com',
    'https://stackoverflow.com/questions/tagged/javascript',
    'https://developer.mozilla.org/en-US/docs/Web/JavaScript'
  ];

  const exampleFileUrls = [
    'file:///Users/<USER>/Documents/page.html',
    'file:///C:/Users/<USER>/Documents/page.html',
    'file:///home/<USER>/documents/page.html'
  ];

  const handleExampleClick = (exampleUrl) => {
    setUrl(exampleUrl);
    setError('');
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          Web2MD - Web Page to Markdown Converter
        </h1>
        <p className="text-gray-600">
          Convert any web page or HTML file to clean, formatted Markdown
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          onClick={() => setActiveTab('url')}
          className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
            activeTab === 'url'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
          disabled={isLoading}
        >
          URL Input
        </button>
        <button
          onClick={() => setActiveTab('file')}
          className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
            activeTab === 'file'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
          disabled={isLoading}
        >
          File Upload
        </button>
      </div>

      {/* URL Input Tab */}
      {activeTab === 'url' && (
        <div className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
                Enter Website URL or File Path
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  id="url"
                  value={url}
                  onChange={handleInputChange}
                  placeholder="https://example.com or file:///path/to/file.html"
                  className={`flex-1 px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors ${
                    error ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                />
                <button
                  type="submit"
                  disabled={isLoading || !url.trim()}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Converting...
                    </div>
                  ) : (
                    'Convert to Markdown'
                  )}
                </button>
              </div>
              {error && (
                <p className="mt-2 text-sm text-red-600">{error}</p>
              )}
            </div>
          </form>
        </div>
      )}

      {/* File Upload Tab */}
      {activeTab === 'file' && (
        <div className="space-y-6">
          <FileUpload onFileUpload={handleFileUpload} isLoading={isLoading} />
        </div>
      )}

      {/* Examples Section */}
      {activeTab === 'url' && (
        <div className="mt-6">
          <p className="text-sm text-gray-600 mb-3">Try these examples:</p>
          <div className="space-y-3">
            <div>
              <p className="text-xs text-gray-500 mb-2">Web URLs:</p>
              <div className="flex flex-wrap gap-2">
                {exampleUrls.map((exampleUrl, index) => (
                  <button
                    key={index}
                    onClick={() => handleExampleClick(exampleUrl)}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                    disabled={isLoading}
                  >
                    {exampleUrl}
                  </button>
                ))}
              </div>
            </div>
            <div>
              <p className="text-xs text-gray-500 mb-2">File URLs:</p>
              <div className="flex flex-wrap gap-2">
                {exampleFileUrls.map((exampleUrl, index) => (
                  <button
                    key={`file-${index}`}
                    onClick={() => handleExampleClick(exampleUrl)}
                    className="px-3 py-1 text-sm bg-yellow-100 text-yellow-700 rounded-full hover:bg-yellow-200 transition-colors"
                    disabled={isLoading}
                  >
                    {exampleUrl}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-sm font-medium text-blue-800 mb-2">How it works:</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          {activeTab === 'url' ? (
            <>
              <li>• Enter any website URL or file:// path</li>
              <li>• We fetch and clean the content</li>
              <li>• Convert HTML to beautiful Markdown</li>
              <li>• Download or copy the result</li>
            </>
          ) : (
            <>
              <li>• Upload your HTML file (.html or .htm)</li>
              <li>• We process and clean the content</li>
              <li>• Convert HTML to beautiful Markdown</li>
              <li>• Download or copy the result</li>
            </>
          )}
        </ul>

        {activeTab === 'url' && (
          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <p className="text-xs text-yellow-800">
              <strong>Note:</strong> file:// URLs may not work in all browsers due to security restrictions.
              Use the File Upload tab for better compatibility with local files.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default URLInput;
