import { useState, useEffect } from 'react';

const MarkdownViewer = ({ markdown, title, url, isLoading }) => {
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (copied) {
      const timer = setTimeout(() => setCopied(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [copied]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(markdown);
      setCopied(true);
    } catch (error) {
      console.error('Failed to copy:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = markdown;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
    }
  };

  const handleDownload = () => {
    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${generateFilename(title)}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const generateFilename = (pageTitle) => {
    if (!pageTitle || pageTitle === 'Untitled Page') {
      return 'webpage';
    }
    
    // Clean the title to make it a valid filename
    return pageTitle
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .substring(0, 50) || 'webpage'; // Limit length
  };

  const getWordCount = (text) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  const getCharCount = (text) => {
    return text.length;
  };

  const getLineCount = (text) => {
    return text.split('\n').length;
  };

  if (isLoading) {
    return (
      <div className="h-full flex flex-col bg-white rounded-lg shadow-sm border">
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900">Markdown Output</h3>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-lg font-medium">Converting to Markdown...</p>
            <p className="text-sm">Please wait while we process the web page</p>
          </div>
        </div>
      </div>
    );
  }

  if (!markdown) {
    return (
      <div className="h-full flex flex-col bg-white rounded-lg shadow-sm border">
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900">Markdown Output</h3>
        </div>
        <div className="flex-1 flex items-center justify-center bg-gray-50">
          <div className="text-center text-gray-500">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-lg font-medium">Markdown Preview</p>
            <p className="text-sm">Converted markdown will appear here</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white rounded-lg shadow-sm border">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-medium text-gray-900">Markdown Output</h3>
          <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
            <span>{getWordCount(markdown)} words</span>
            <span>{getCharCount(markdown)} characters</span>
            <span>{getLineCount(markdown)} lines</span>
          </div>
        </div>
        <div className="flex items-center gap-2 ml-4">
          <button
            onClick={handleCopy}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              copied 
                ? 'bg-green-100 text-green-700' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {copied ? 'Copied!' : 'Copy'}
          </button>
          <button
            onClick={handleDownload}
            className="px-3 py-1 text-xs bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
          >
            Download
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-4">
          <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono leading-relaxed">
            {markdown}
          </pre>
        </div>
      </div>

      {/* Footer with metadata */}
      {(title || url) && (
        <div className="border-t bg-gray-50 p-3">
          <div className="text-xs text-gray-600">
            {title && (
              <div className="mb-1">
                <span className="font-medium">Title:</span> {title}
              </div>
            )}
            {url && (
              <div>
                <span className="font-medium">Source:</span> 
                <a 
                  href={url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 ml-1"
                >
                  {url}
                </a>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MarkdownViewer;
