import { useState } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import URLInput from './components/URLInput';
import WebPageViewer from './components/WebPageViewer';
import MarkdownViewer from './components/MarkdownViewer';
import ManualSaveGuide from './components/ManualSaveGuide';
import { fetchWebPage, processUploadedFile } from './utils/webScraper';
import { convertHtmlToMarkdown, extractMainContent } from './utils/htmlToMarkdown';

function App() {
  const [isLoading, setIsLoading] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');
  const [pageData, setPageData] = useState({
    html: '',
    title: '',
    url: ''
  });
  const [markdown, setMarkdown] = useState('');
  const [error, setError] = useState('');
  const [showResults, setShowResults] = useState(false);

  const handleUrlSubmit = async (url) => {
    setIsLoading(true);
    setError('');
    setCurrentUrl(url);

    try {
      // Fetch the web page
      const { html, title, url: finalUrl } = await fetchWebPage(url);

      // Extract main content
      const mainContent = extractMainContent(html);

      // Convert to markdown
      const markdownContent = convertHtmlToMarkdown(mainContent);

      // Update state
      setPageData({
        html: mainContent,
        title,
        url: finalUrl
      });
      setMarkdown(markdownContent);
      setShowResults(true);

    } catch (error) {
      console.error('Error processing URL:', error);
      setError(error.message || 'Failed to process the web page. Please try again.');
      setShowResults(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = async (file) => {
    setIsLoading(true);
    setError('');
    setCurrentUrl(file.name);

    try {
      // Process the uploaded file
      const { html, title, url: fileUrl } = await processUploadedFile(file);

      // Extract main content
      const mainContent = extractMainContent(html);

      // Convert to markdown
      const markdownContent = convertHtmlToMarkdown(mainContent);

      // Update state
      setPageData({
        html: mainContent,
        title,
        url: fileUrl
      });
      setMarkdown(markdownContent);
      setShowResults(true);

    } catch (error) {
      console.error('Error processing file:', error);
      setError(error.message || 'Failed to process the HTML file. Please try again.');
      setShowResults(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setShowResults(false);
    setPageData({ html: '', title: '', url: '' });
    setMarkdown('');
    setError('');
    setCurrentUrl('');
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {!showResults ? (
        <div className="container mx-auto px-4 py-8">
          <URLInput
            onUrlSubmit={handleUrlSubmit}
            onFileUpload={handleFileUpload}
            isLoading={isLoading}
          />

          {error && (
            <div className="max-w-4xl mx-auto mt-6">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <p className="text-red-700">{error}</p>
                </div>
              </div>

              {/* Show manual save guide for certain types of errors */}
              {(error.includes('403') || error.includes('CORS') || error.includes('proxies failed') || error.includes('blocks automated')) && (
                <ManualSaveGuide url={currentUrl} />
              )}
            </div>
          )}
        </div>
      ) : (
        <div className="h-screen flex flex-col">
          {/* Header */}
          <div className="bg-white border-b px-6 py-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={handleReset}
                className="flex items-center gap-2 px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                New Conversion
              </button>
              <div className="text-sm text-gray-600">
                <span className="font-medium">Converting:</span> {currentUrl}
              </div>
            </div>
            <div className="text-sm text-gray-500">
              Web2MD - Web Page to Markdown Converter
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6">
            <PanelGroup direction="horizontal" className="h-full">
              <Panel defaultSize={50} minSize={30}>
                <WebPageViewer
                  url={pageData.url}
                  html={pageData.html}
                  title={pageData.title}
                />
              </Panel>

              <PanelResizeHandle className="w-2 bg-gray-200 hover:bg-gray-300 transition-colors cursor-col-resize" />

              <Panel defaultSize={50} minSize={30}>
                <MarkdownViewer
                  markdown={markdown}
                  title={pageData.title}
                  url={pageData.url}
                  isLoading={isLoading}
                />
              </Panel>
            </PanelGroup>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
