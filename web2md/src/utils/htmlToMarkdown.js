import TurndownService from 'turndown';

// Create a new Turndown service instance with custom options
const turndownService = new TurndownService({
  headingStyle: 'atx',
  hr: '---',
  bulletListMarker: '-',
  codeBlockStyle: 'fenced',
  fence: '```',
  emDelimiter: '_',
  strongDelimiter: '**',
  linkStyle: 'inlined',
  linkReferenceStyle: 'full',
  br: '  \n'
});

// Add custom rules for better conversion
turndownService.addRule('strikethrough', {
  filter: ['del', 's', 'strike'],
  replacement: function (content) {
    return '~~' + content + '~~';
  }
});

// Remove script and style tags completely
turndownService.addRule('removeScripts', {
  filter: ['script', 'style', 'noscript'],
  replacement: function () {
    return '';
  }
});

// Handle images better
turndownService.addRule('images', {
  filter: 'img',
  replacement: function (content, node) {
    const alt = node.getAttribute('alt') || '';
    const src = node.getAttribute('src') || '';
    const title = node.getAttribute('title');
    const titlePart = title ? ' "' + title + '"' : '';
    return src ? '![' + alt + '](' + src + titlePart + ')' : '';
  }
});

// Handle tables better
turndownService.addRule('tables', {
  filter: 'table',
  replacement: function (content, node) {
    // Simple table handling - could be enhanced
    return '\n' + content + '\n';
  }
});

/**
 * Convert HTML content to Markdown
 * @param {string} html - The HTML content to convert
 * @returns {string} - The converted Markdown content
 */
export function convertHtmlToMarkdown(html) {
  try {
    // Clean up the HTML first
    const cleanedHtml = cleanHtml(html);
    
    // Convert to markdown
    const markdown = turndownService.turndown(cleanedHtml);
    
    // Post-process the markdown
    return postProcessMarkdown(markdown);
  } catch (error) {
    console.error('Error converting HTML to Markdown:', error);
    return 'Error converting content to Markdown. Please try again.';
  }
}

/**
 * Clean HTML content before conversion
 * @param {string} html - Raw HTML content
 * @returns {string} - Cleaned HTML content
 */
function cleanHtml(html) {
  // Remove comments
  let cleaned = html.replace(/<!--[\s\S]*?-->/g, '');
  
  // Remove script and style content
  cleaned = cleaned.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  cleaned = cleaned.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '');
  
  // Remove navigation, footer, sidebar elements that are typically not content
  cleaned = cleaned.replace(/<nav\b[^>]*>[\s\S]*?<\/nav>/gi, '');
  cleaned = cleaned.replace(/<footer\b[^>]*>[\s\S]*?<\/footer>/gi, '');
  cleaned = cleaned.replace(/<aside\b[^>]*>[\s\S]*?<\/aside>/gi, '');
  cleaned = cleaned.replace(/<header\b[^>]*>[\s\S]*?<\/header>/gi, '');
  
  // Remove ads and promotional content
  cleaned = cleaned.replace(/<div[^>]*class="[^"]*ad[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '');
  cleaned = cleaned.replace(/<div[^>]*class="[^"]*banner[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '');
  
  return cleaned;
}

/**
 * Post-process markdown to improve formatting
 * @param {string} markdown - Raw markdown content
 * @returns {string} - Cleaned markdown content
 */
function postProcessMarkdown(markdown) {
  // Remove excessive blank lines
  let processed = markdown.replace(/\n{3,}/g, '\n\n');
  
  // Clean up list formatting
  processed = processed.replace(/^\s*[-*+]\s*$/gm, '');
  
  // Remove empty links
  processed = processed.replace(/\[\]\([^)]*\)/g, '');
  
  // Clean up whitespace
  processed = processed.trim();
  
  return processed;
}

/**
 * Extract main content from HTML (attempt to get article content)
 * @param {string} html - Full HTML content
 * @returns {string} - Extracted main content HTML
 */
export function extractMainContent(html) {
  // Create a temporary DOM element to parse HTML
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  
  // Try to find main content using common selectors
  const contentSelectors = [
    'article',
    'main',
    '[role="main"]',
    '.content',
    '.post-content',
    '.entry-content',
    '.article-content',
    '#content',
    '#main-content',
    '.main-content'
  ];
  
  for (const selector of contentSelectors) {
    const element = doc.querySelector(selector);
    if (element && element.textContent.trim().length > 100) {
      return element.outerHTML;
    }
  }
  
  // If no main content found, try to extract from body
  const body = doc.querySelector('body');
  if (body) {
    // Remove unwanted elements
    const unwantedSelectors = [
      'nav', 'header', 'footer', 'aside', 
      '.navigation', '.sidebar', '.menu',
      '.ads', '.advertisement', '.banner',
      'script', 'style', 'noscript'
    ];
    
    unwantedSelectors.forEach(selector => {
      const elements = body.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });
    
    return body.innerHTML;
  }
  
  // Fallback to original HTML
  return html;
}
