import axios from 'axios';

/**
 * Fetch web page content with CORS handling
 * @param {string} url - The URL to fetch
 * @returns {Promise<{html: string, title: string, url: string}>} - The fetched content
 */
export async function fetchWebPage(url) {
  try {
    // Validate URL
    const validatedUrl = validateAndNormalizeUrl(url);

    // Handle file:// URLs
    if (validatedUrl.startsWith('file://')) {
      return await fetchLocalFile(validatedUrl);
    }

    // Try direct fetch first (will work for same-origin or CORS-enabled sites)
    try {
      const response = await fetch(validatedUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; Web2MD/1.0)',
        },
        mode: 'cors'
      });

      if (response.ok) {
        const html = await response.text();
        const title = extractTitleFromHtml(html);
        return { html, title, url: validatedUrl };
      }
    } catch (corsError) {
      console.log('Direct fetch failed, trying CORS proxy...', corsError.message);
    }

    // If direct fetch fails, try with multiple CORS proxies
    const proxies = [
      {
        name: 'AllOrig<PERSON>',
        url: `https://api.allorigins.win/get?url=${encodeURIComponent(validatedUrl)}`,
        extractContent: (data) => data.contents
      },
      {
        name: 'CORS Anywhere (Heroku)',
        url: `https://cors-anywhere.herokuapp.com/${validatedUrl}`,
        extractContent: (data) => data,
        isText: true
      },
      {
        name: 'ThingProxy',
        url: `https://thingproxy.freeboard.io/fetch/${validatedUrl}`,
        extractContent: (data) => data,
        isText: true
      }
    ];

    let lastError = null;

    for (const proxy of proxies) {
      try {
        console.log(`Trying ${proxy.name} proxy...`);

        const response = await axios.get(proxy.url, {
          timeout: 30000,
          headers: {
            'Accept': proxy.isText ? 'text/html' : 'application/json',
            'User-Agent': 'Mozilla/5.0 (compatible; Web2MD/1.0)',
          }
        });

        const html = proxy.extractContent(response.data);

        if (html && html.length > 100) { // Basic content validation
          const title = extractTitleFromHtml(html);
          return { html, title, url: validatedUrl };
        }
      } catch (proxyError) {
        console.log(`${proxy.name} proxy failed:`, proxyError.message);
        lastError = proxyError;
        continue; // Try next proxy
      }
    }

    // If all proxies fail, throw the last error
    throw lastError || new Error('All CORS proxies failed to fetch the content');

  } catch (error) {
    console.error('Error fetching web page:', error);

    // Provide more specific error messages
    if (error.code === 'ENOTFOUND' || error.message.includes('Network Error')) {
      throw new Error('Unable to reach the website. Please check the URL and your internet connection.');
    } else if (error.response?.status === 404) {
      throw new Error('Page not found (404). Please check the URL.');
    } else if (error.response?.status === 403) {
      throw new Error('Access forbidden (403). This website blocks automated requests and CORS proxies. Try downloading the page manually and using the file upload feature instead.');
    } else if (error.response?.status === 429) {
      throw new Error('Rate limited (429). The website or proxy is temporarily blocking requests. Please try again later.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error. The website may be temporarily unavailable.');
    } else if (error.message.includes('timeout')) {
      throw new Error('Request timed out. The website may be slow to respond.');
    } else if (error.message.includes('CORS') || error.message.includes('Cross-Origin')) {
      throw new Error('CORS error. This website blocks cross-origin requests. Try downloading the page manually and using the file upload feature.');
    } else if (error.message.includes('All CORS proxies failed')) {
      throw new Error('Unable to access this website through available proxies. The site may have strong anti-bot protection. Try saving the page manually (Ctrl+S) and uploading the HTML file instead.');
    } else {
      throw new Error(`Failed to fetch the web page: ${error.message}`);
    }
  }
}

/**
 * Fetch local HTML file
 * @param {string} fileUrl - The file:// URL
 * @returns {Promise<{html: string, title: string, url: string}>} - The fetched content
 */
async function fetchLocalFile(fileUrl) {
  try {
    const response = await fetch(fileUrl);

    if (!response.ok) {
      throw new Error(`Failed to load local file: ${response.status} ${response.statusText}`);
    }

    const html = await response.text();
    const title = extractTitleFromHtml(html);

    return { html, title, url: fileUrl };
  } catch (error) {
    console.error('Error fetching local file:', error);

    if (error.message.includes('Failed to fetch')) {
      throw new Error('Cannot access local file. For security reasons, browsers restrict access to local files. Please use the file upload option instead.');
    }

    throw new Error(`Failed to load local file: ${error.message}`);
  }
}

/**
 * Process uploaded HTML file
 * @param {File} file - The uploaded HTML file
 * @returns {Promise<{html: string, title: string, url: string}>} - The processed content
 */
export async function processUploadedFile(file) {
  try {
    // Validate file type
    if (!file.type.includes('html') && !file.name.toLowerCase().endsWith('.html') && !file.name.toLowerCase().endsWith('.htm')) {
      throw new Error('Please upload an HTML file (.html or .htm)');
    }

    // Check file size (limit to 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Error('File is too large. Please upload a file smaller than 10MB.');
    }

    // Read file content
    const html = await readFileAsText(file);
    const title = extractTitleFromHtml(html);

    return {
      html,
      title,
      url: `file://${file.name}`
    };
  } catch (error) {
    console.error('Error processing uploaded file:', error);
    throw error;
  }
}

/**
 * Read file as text
 * @param {File} file - The file to read
 * @returns {Promise<string>} - The file content as text
 */
function readFileAsText(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      resolve(event.target.result);
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsText(file);
  });
}

/**
 * Validate and normalize URL
 * @param {string} url - The URL to validate
 * @returns {string} - The normalized URL
 */
function validateAndNormalizeUrl(url) {
  if (!url || typeof url !== 'string') {
    throw new Error('Please enter a valid URL');
  }

  // Remove whitespace
  url = url.trim();

  // Handle file:// URLs
  if (url.startsWith('file://')) {
    try {
      const urlObj = new URL(url);
      return urlObj.href;
    } catch (error) {
      throw new Error('Invalid file:// URL format. Please enter a valid file path.');
    }
  }

  // Add protocol if missing for web URLs
  if (!url.match(/^https?:\/\//)) {
    url = 'https://' + url;
  }

  // Validate URL format
  try {
    const urlObj = new URL(url);

    // Check for valid protocols
    if (!['http:', 'https:', 'file:'].includes(urlObj.protocol)) {
      throw new Error('Only HTTP, HTTPS, and file:// URLs are supported');
    }

    // Check for valid hostname (not required for file:// URLs)
    if (urlObj.protocol !== 'file:' && (!urlObj.hostname || urlObj.hostname.length === 0)) {
      throw new Error('Invalid hostname in URL');
    }

    return urlObj.href;
  } catch (error) {
    if (error instanceof TypeError) {
      throw new Error('Invalid URL format. Please enter a valid web address or file:// URL.');
    }
    throw error;
  }
}

/**
 * Extract title from HTML content
 * @param {string} html - The HTML content
 * @returns {string} - The extracted title
 */
function extractTitleFromHtml(html) {
  try {
    // Try to extract title using regex (more reliable than DOM parsing for this use case)
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch && titleMatch[1]) {
      return titleMatch[1].trim();
    }

    // Try meta title
    const metaTitleMatch = html.match(/<meta[^>]*property=["']og:title["'][^>]*content=["']([^"']+)["']/i);
    if (metaTitleMatch && metaTitleMatch[1]) {
      return metaTitleMatch[1].trim();
    }

    // Try h1 as fallback
    const h1Match = html.match(/<h1[^>]*>([^<]+)<\/h1>/i);
    if (h1Match && h1Match[1]) {
      return h1Match[1].trim();
    }

    return 'Untitled Page';
  } catch (error) {
    console.error('Error extracting title:', error);
    return 'Untitled Page';
  }
}

/**
 * Check if URL is accessible (basic validation)
 * @param {string} url - The URL to check
 * @returns {Promise<boolean>} - Whether the URL is accessible
 */
export async function isUrlAccessible(url) {
  try {
    const validatedUrl = validateAndNormalizeUrl(url);

    // Try a HEAD request first (faster)
    const response = await fetch(validatedUrl, {
      method: 'HEAD',
      mode: 'no-cors', // This will always succeed but we can't read the response
      timeout: 10000
    });

    return true; // If we get here, the request didn't fail
  } catch (error) {
    console.log('URL accessibility check failed:', error.message);
    return false;
  }
}

/**
 * Get website favicon URL
 * @param {string} url - The website URL
 * @returns {string} - The favicon URL
 */
export function getFaviconUrl(url) {
  try {
    const urlObj = new URL(url);
    return `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
  } catch (error) {
    return null;
  }
}
