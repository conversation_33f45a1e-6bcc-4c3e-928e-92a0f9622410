# Web2MD - Web Page to Markdown Converter

A beautiful, modern web application that converts any web page to clean, formatted Markdown. Built with React, Vite, and Tailwind CSS.

## Features

- 🌐 **URL Input**: Simply paste any website URL or file:// path
- 📁 **File Upload**: Drag and drop or browse for local HTML files
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 🔄 **Split View**: See the original webpage and converted markdown side-by-side
- 📝 **Clean Conversion**: Uses Turndown.js for high-quality HTML to Markdown conversion
- 💾 **Download Support**: Download the converted markdown as a .md file
- 📋 **Copy to Clipboard**: One-click copy functionality
- 🎨 **Beautiful UI**: Modern, clean interface with Tailwind CSS
- ⚡ **Fast**: Built with Vite for lightning-fast development and builds
- 🔍 **Content Extraction**: Automatically extracts main content from web pages
- 📊 **Statistics**: Shows word count, character count, and line count
- 🔒 **Local File Support**: Process local HTML files securely in your browser

## Technology Stack

- **Frontend Framework**: React 19
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **HTML to Markdown**: Turndown.js
- **HTTP Client**: Axios
- **Layout**: React Resizable Panels
- **CORS Handling**: AllOrigins proxy service

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/golgolak/web2md.git
cd web2md
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Usage

### Option 1: URL Input
1. **Enter a URL**: Type or paste any website URL or file:// path in the input field
2. **Convert**: Click "Convert to Markdown" button
3. **View Results**: See the original webpage on the left and converted markdown on the right
4. **Download or Copy**: Use the buttons to download the markdown file or copy to clipboard

### Option 2: File Upload
1. **Upload File**: Switch to the "File Upload" tab
2. **Select File**: Drag and drop an HTML file or click to browse
3. **Process**: The file will be automatically processed
4. **View Results**: See the original content on the left and converted markdown on the right
5. **Download or Copy**: Use the buttons to download the markdown file or copy to clipboard

### General
- **New Conversion**: Click "New Conversion" to start over with a new URL or file

## Features in Detail

### Smart Content Extraction
The application automatically identifies and extracts the main content from web pages, filtering out:
- Navigation menus
- Sidebars
- Advertisements
- Headers and footers
- Scripts and styles

### High-Quality Markdown Conversion
- Preserves formatting (headings, lists, links, images)
- Handles tables and code blocks
- Maintains text structure and hierarchy
- Removes unnecessary HTML elements

### CORS Handling
The application handles Cross-Origin Resource Sharing (CORS) issues by:
1. First attempting direct fetch for CORS-enabled sites
2. Falling back to multiple CORS proxy services for restricted sites
3. Providing helpful error messages and manual save instructions for protected sites

### Local File Support
- **File Upload**: Securely process local HTML files using the browser's File API
- **file:// URLs**: Support for file:// URLs (with browser security limitations)
- **Drag & Drop**: Intuitive drag and drop interface for file uploads
- **File Validation**: Automatic validation of file types and sizes
- **Security**: All processing happens locally in your browser

## Limitations and Workarounds

### Protected Websites
Some websites (like umod.org, many corporate sites, etc.) use anti-bot protection that blocks automated access:

**Symptoms:**
- 403 Forbidden errors
- "Just a moment..." challenge pages
- Cloudflare or similar protection screens

**Solution:**
1. **Manual Save Method** (Recommended):
   - Visit the website in your browser
   - Save the page manually (Ctrl+S or Cmd+S)
   - Use the "File Upload" tab to upload the saved HTML file

2. **Browser Extensions**: Some browser extensions can help bypass certain restrictions

3. **Alternative URLs**: Sometimes the same content is available on different domains or subdomains

### CORS Limitations
- Some websites block cross-origin requests entirely
- Government and financial sites often have strict policies
- The app tries multiple proxy services but some sites block all proxies

### File Upload Advantages
- **Always works**: No network restrictions
- **Privacy**: Content never leaves your browser
- **Offline**: Works without internet connection
- **Complete content**: Includes all embedded resources when saved as "complete webpage"

## Project Structure

```
src/
├── components/
│   ├── URLInput.jsx          # URL input and file upload component
│   ├── FileUpload.jsx        # Drag & drop file upload component
│   ├── WebPageViewer.jsx     # Left pane - displays original webpage
│   └── MarkdownViewer.jsx    # Right pane - displays converted markdown
├── utils/
│   ├── htmlToMarkdown.js     # HTML to Markdown conversion utilities
│   └── webScraper.js         # Web page fetching and file processing utilities
├── App.jsx                   # Main application component
├── main.jsx                  # Application entry point
└── index.css                 # Global styles with Tailwind
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [Turndown.js](https://github.com/mixmark-io/turndown) for excellent HTML to Markdown conversion
- [AllOrigins](https://allorigins.win/) for CORS proxy service
- [Tailwind CSS](https://tailwindcss.com/) for beautiful styling
- [React Resizable Panels](https://github.com/bvaughn/react-resizable-panels) for the split layout
