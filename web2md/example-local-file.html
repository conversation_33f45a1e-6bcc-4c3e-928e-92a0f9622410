<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>uMod - Approval Guide</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f0f0f0; padding: 20px; }
        .content { margin: 20px 0; }
        .code-block { background: #f5f5f5; padding: 15px; border-left: 4px solid #007acc; }
    </style>
</head>
<body>
    <header class="header">
        <h1>uMod - Approval Guide</h1>
        <p>Guidelines for plugin approval on the uMod platform</p>
    </header>
    
    <main class="content">
        <h2>Overview</h2>
        <p>This guide outlines the requirements and process for getting your plugin approved on the uMod platform.</p>
        
        <h2>Requirements</h2>
        <ul>
            <li><strong>Code Quality</strong>: Your code must be well-structured and documented</li>
            <li><strong>Functionality</strong>: The plugin must work as described</li>
            <li><strong>Security</strong>: No malicious code or security vulnerabilities</li>
            <li><strong>Performance</strong>: Efficient resource usage</li>
        </ul>
        
        <h3>Code Example</h3>
        <div class="code-block">
            <pre><code>namespace Oxide.Plugins
{
    [Info("MyPlugin", "Author", "1.0.0")]
    public class MyPlugin : RustPlugin
    {
        void Init()
        {
            Puts("Plugin loaded successfully!");
        }
    }
}</code></pre>
        </div>
        
        <h2>Submission Process</h2>
        <ol>
            <li>Prepare your plugin according to the guidelines</li>
            <li>Test thoroughly on a development server</li>
            <li>Submit through the uMod portal</li>
            <li>Wait for review (typically 3-5 business days)</li>
            <li>Address any feedback from reviewers</li>
        </ol>
        
        <blockquote>
            <p><strong>Note:</strong> Plugins that don't meet the quality standards will be rejected with detailed feedback.</p>
        </blockquote>
        
        <h2>Best Practices</h2>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <thead>
                <tr>
                    <th>Category</th>
                    <th>Recommendation</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Documentation</td>
                    <td>Include clear README with usage instructions</td>
                </tr>
                <tr>
                    <td>Error Handling</td>
                    <td>Implement proper try-catch blocks</td>
                </tr>
                <tr>
                    <td>Configuration</td>
                    <td>Use config files for customizable options</td>
                </tr>
            </tbody>
        </table>
        
        <h2>Contact</h2>
        <p>For questions about the approval process, contact the uMod team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
    </main>
    
    <footer>
        <p>&copy; 2024 uMod Platform. All rights reserved.</p>
    </footer>
</body>
</html>
